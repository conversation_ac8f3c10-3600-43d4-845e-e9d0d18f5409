import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { ToastService } from '../../services/toast.service';
import { ToastComponent, ToastConfig } from '../toast/toast.component';

@Component({
  selector: 'app-toast-container',
  standalone: true,
  imports: [CommonModule, ToastComponent],
  template: `
    <div class="toast-container-wrapper">
      <!-- Group toasts by position -->
      <div 
        *ngFor="let position of positions" 
        class="toast-position-group"
        [ngClass]="'toast-group-' + position">
        
        <app-toast
          *ngFor="let toast of getToastsByPosition(position); trackBy: trackByToastId"
          [config]="toast"
          (dismissed)="onToastDismissed($event)">
        </app-toast>
      </div>
    </div>
  `,
  styles: [`
    .toast-container-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: var(--z-tooltip);
    }

    .toast-position-group {
      position: absolute;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      max-height: calc(100vh - 2rem);
      overflow-y: auto;
      pointer-events: none;
    }

    .toast-position-group.toast-group-top-right {
      top: 1rem;
      right: 1rem;
    }

    .toast-position-group.toast-group-top-left {
      top: 1rem;
      left: 1rem;
    }

    .toast-position-group.toast-group-bottom-right {
      bottom: 1rem;
      right: 1rem;
      flex-direction: column-reverse;
    }

    .toast-position-group.toast-group-bottom-left {
      bottom: 1rem;
      left: 1rem;
      flex-direction: column-reverse;
    }

    .toast-position-group.toast-group-top-center {
      top: 1rem;
      left: 50%;
      transform: translateX(-50%);
    }

    .toast-position-group.toast-group-bottom-center {
      bottom: 1rem;
      left: 50%;
      transform: translateX(-50%);
      flex-direction: column-reverse;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .toast-position-group {
        left: 1rem !important;
        right: 1rem !important;
        transform: none !important;
        max-width: calc(100vw - 2rem);
      }
    }

    @media (max-width: 480px) {
      .toast-position-group {
        left: 0.5rem !important;
        right: 0.5rem !important;
        max-width: calc(100vw - 1rem);
      }
    }
  `]
})
export class ToastContainerComponent implements OnInit, OnDestroy {
  toasts: ToastConfig[] = [];
  positions: string[] = ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center'];
  
  private subscription?: Subscription;

  constructor(private toastService: ToastService) {}

  ngOnInit(): void {
    this.subscription = this.toastService.toasts$.subscribe(toasts => {
      this.toasts = toasts;
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  getToastsByPosition(position: string): ToastConfig[] {
    return this.toasts.filter(toast => (toast.position || 'top-right') === position);
  }

  onToastDismissed(toastId: string): void {
    this.toastService.remove(toastId);
  }

  trackByToastId(index: number, toast: ToastConfig): string {
    return toast.id || index.toString();
  }
}
