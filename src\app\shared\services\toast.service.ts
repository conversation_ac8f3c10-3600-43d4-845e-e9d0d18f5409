import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ToastConfig } from '../components/toast/toast.component';

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toastsSubject = new BehaviorSubject<ToastConfig[]>([]);
  public toasts$: Observable<ToastConfig[]> = this.toastsSubject.asObservable();

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private addToast(config: ToastConfig): void {
    const toast: ToastConfig = {
      id: this.generateId(),
      duration: 5000,
      dismissible: true,
      position: 'top-right',
      ...config
    };

    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, toast]);
  }

  /**
   * Show a success toast notification
   */
  success(message: string, title?: string, options?: Partial<ToastConfig>): void {
    this.addToast({
      type: 'success',
      message,
      title,
      ...options
    });
  }

  /**
   * Show an error toast notification
   */
  error(message: string, title?: string, options?: Partial<ToastConfig>): void {
    this.addToast({
      type: 'error',
      message,
      title,
      duration: 7000, // Longer duration for errors
      ...options
    });
  }

  /**
   * Show a warning toast notification
   */
  warning(message: string, title?: string, options?: Partial<ToastConfig>): void {
    this.addToast({
      type: 'warning',
      message,
      title,
      duration: 6000,
      ...options
    });
  }

  /**
   * Show an info toast notification
   */
  info(message: string, title?: string, options?: Partial<ToastConfig>): void {
    this.addToast({
      type: 'info',
      message,
      title,
      ...options
    });
  }

  /**
   * Show a custom toast notification
   */
  show(config: ToastConfig): void {
    this.addToast(config);
  }

  /**
   * Remove a specific toast by ID
   */
  remove(id: string): void {
    const currentToasts = this.toastsSubject.value;
    const filteredToasts = currentToasts.filter(toast => toast.id !== id);
    this.toastsSubject.next(filteredToasts);
  }

  /**
   * Clear all toast notifications
   */
  clear(): void {
    this.toastsSubject.next([]);
  }

  /**
   * Get current toasts
   */
  getToasts(): ToastConfig[] {
    return this.toastsSubject.value;
  }

  /**
   * Medical-themed convenience methods
   */
  
  /**
   * Show donation success message
   */
  donationSuccess(donorName?: string): void {
    this.success(
      donorName 
        ? `Thank you ${donorName}! Your donation has been successfully recorded.`
        : 'Donation has been successfully recorded.',
      'Donation Successful',
      { duration: 6000 }
    );
  }

  /**
   * Show appointment confirmation
   */
  appointmentConfirmed(date?: string): void {
    this.success(
      date 
        ? `Your appointment for ${date} has been confirmed.`
        : 'Your appointment has been confirmed.',
      'Appointment Confirmed',
      { duration: 5000 }
    );
  }

  /**
   * Show blood request approved message
   */
  bloodRequestApproved(bloodType?: string, amount?: number): void {
    const message = bloodType && amount 
      ? `Blood request for ${amount} units of ${bloodType} has been approved.`
      : 'Blood request has been approved.';
    
    this.success(message, 'Request Approved', { duration: 6000 });
  }

  /**
   * Show low stock warning
   */
  lowStockWarning(bloodType: string, currentAmount: number): void {
    this.warning(
      `${bloodType} blood type is running low. Only ${currentAmount} units remaining.`,
      'Low Stock Alert',
      { duration: 8000 }
    );
  }

  /**
   * Show critical stock alert
   */
  criticalStockAlert(bloodType: string, currentAmount: number): void {
    this.error(
      `CRITICAL: ${bloodType} blood type is critically low with only ${currentAmount} units remaining.`,
      'Critical Stock Alert',
      { duration: 10000, dismissible: true }
    );
  }

  /**
   * Show system maintenance notice
   */
  maintenanceNotice(startTime?: string): void {
    this.info(
      startTime 
        ? `System maintenance scheduled for ${startTime}. Some features may be temporarily unavailable.`
        : 'System maintenance in progress. Some features may be temporarily unavailable.',
      'Maintenance Notice',
      { duration: 8000 }
    );
  }
}
