<div class="modern-blood-card hover-lift" [attr.aria-label]="'Blood type ' + inventory.bloodType + ' with ' + inventory.amount + ' units available'">
  <div class="blood-card-header">
    <div class="blood-type-badge" [ngClass]="getBloodTypeClass(inventory.bloodType)">
      <i class="bi bi-droplet-fill"></i>
      <span class="blood-type-text">{{ inventory.bloodType }}</span>
    </div>
    <div class="status-indicator" [ngClass]="getStatusClass()">
      <i class="bi" [ngClass]="getStatusIcon()"></i>
    </div>
  </div>

  <div class="blood-card-body">
    <!-- Circular Progress Indicator -->
    <div class="progress-container">
      <div
        class="circular-progress"
        role="progressbar"
        [attr.aria-valuenow]="roundedAmount"
        aria-valuemin="0"
        aria-valuemax="100"
        [style.--progress]="roundedAmount + '%'">
        <div class="progress-inner">
          <div class="progress-value">
            <span class="amount">{{ inventory.amount }}</span>
            <span class="unit">Units</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Blood Info -->
    <div class="blood-info">
      <div class="info-item">
        <span class="info-label">Available</span>
        <span class="info-value">{{ inventory.amount }} Units</span>
      </div>
      <div class="info-item" *ngIf="inventory.lastDonation">
        <span class="info-label">Last Donation</span>
        <span class="info-value">{{ formatDate(inventory.lastDonation) }}</span>
      </div>
      <div class="info-item" *ngIf="inventory.expirationDate">
        <span class="info-label">Expires</span>
        <span class="info-value" [ngClass]="getExpirationClass()">
          {{ formatDate(inventory.expirationDate) }}
        </span>
      </div>
    </div>
  </div>

  <div class="blood-card-footer">
    <div class="urgency-indicator" [ngClass]="getUrgencyClass()">
      <i class="bi" [ngClass]="getUrgencyIcon()"></i>
      <span>{{ getUrgencyText() }}</span>
    </div>
    <button
      class="btn btn-sm btn-outline-primary action-btn"
      (click)="onManageStock()"
      [attr.aria-label]="'Manage ' + inventory.bloodType + ' blood stock'">
      <i class="bi bi-gear me-1"></i>
      Manage
    </button>
  </div>
</div>
