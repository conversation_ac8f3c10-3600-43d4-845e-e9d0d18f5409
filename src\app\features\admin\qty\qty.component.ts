import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { DESIGN_TOKENS, CSS_CLASSES, getBloodTypeClass, getStatusClass, getUrgencyClass } from '../../../constants';

@Component({
  selector: 'app-qty',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './qty.component.html',
  styleUrl: './qty.component.css'
})
export class QtyComponent implements OnInit {
  @Input() public inventory: any;
  @Output() manageStock = new EventEmitter<any>();

  public roundedAmount: number = 0;

  // Design System
  readonly DESIGN = DESIGN_TOKENS;
  readonly CSS = CSS_CLASSES;

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.roundedAmount = Math.round(this.inventory.amount);
    console.log(this.inventory);
  }

  getBloodTypeClass(bloodType: string): string {
    return getBloodTypeClass(bloodType);
  }

  getStatusClass(): string {
    if (this.inventory.amount <= 10) return 'status-critical';
    if (this.inventory.amount <= 25) return 'status-low';
    if (this.inventory.amount <= 50) return 'status-medium';
    return 'status-good';
  }

  getStatusIcon(): string {
    if (this.inventory.amount <= 10) return 'bi-exclamation-triangle-fill';
    if (this.inventory.amount <= 25) return 'bi-exclamation-circle-fill';
    if (this.inventory.amount <= 50) return 'bi-info-circle-fill';
    return 'bi-check-circle-fill';
  }

  getUrgencyClass(): string {
    if (this.inventory.amount <= 10) return CSS_CLASSES.URGENCY.CRITICAL;
    if (this.inventory.amount <= 25) return CSS_CLASSES.URGENCY.HIGH;
    if (this.inventory.amount <= 50) return CSS_CLASSES.URGENCY.MEDIUM;
    return CSS_CLASSES.URGENCY.LOW;
  }

  getUrgencyIcon(): string {
    if (this.inventory.amount <= 10) return 'bi-exclamation-triangle-fill';
    if (this.inventory.amount <= 25) return 'bi-arrow-up-circle-fill';
    if (this.inventory.amount <= 50) return 'bi-dash-circle-fill';
    return 'bi-check-circle-fill';
  }

  getUrgencyText(): string {
    if (this.inventory.amount <= 10) return 'Critical';
    if (this.inventory.amount <= 25) return 'Low Stock';
    if (this.inventory.amount <= 50) return 'Medium';
    return 'Good Stock';
  }

  getExpirationClass(): string {
    if (!this.inventory.expirationDate) return '';

    const expirationDate = new Date(this.inventory.expirationDate);
    const today = new Date();
    const daysUntilExpiration = Math.ceil((expirationDate.getTime() - today.getTime()) / (1000 * 3600 * 24));

    if (daysUntilExpiration <= 7) return 'text-error';
    if (daysUntilExpiration <= 14) return 'text-warning';
    return 'text-success';
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }

  onManageStock(): void {
    this.manageStock.emit(this.inventory);
  }
}
