/* Modern Loading Spinner Styles */

/* Container Styles */
.modern-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.modern-spinner-container.spinner-center {
  padding: 2rem;
}

.modern-spinner-container.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal);
}

.modern-spinner-container.spinner-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--color-white);
  z-index: var(--z-modal);
}

/* Size Classes */
.spinner-sm { font-size: 1rem; }
.spinner-md { font-size: 1.5rem; }
.spinner-lg { font-size: 2rem; }
.spinner-xl { font-size: 3rem; }

/* Blood Drop Spinner */
.spinner-blood-drop {
  display: flex;
  align-items: center;
  justify-content: center;
}

.blood-drop {
  color: var(--color-primary);
  animation: bloodDrop 2s ease-in-out infinite;
  transform-origin: center bottom;
}

@keyframes bloodDrop {
  0%, 100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  25% {
    transform: scale(1.1) translateY(-10px);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.9) translateY(5px);
    opacity: 0.9;
  }
  75% {
    transform: scale(1.05) translateY(-5px);
    opacity: 0.85;
  }
}

/* Heart Beat Spinner */
.spinner-heartbeat {
  display: flex;
  align-items: center;
  justify-content: center;
}

.heartbeat {
  color: var(--color-primary);
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  14% {
    transform: scale(1.3);
    opacity: 0.8;
  }
  28% {
    transform: scale(1);
    opacity: 1;
  }
  42% {
    transform: scale(1.3);
    opacity: 0.8;
  }
  70% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse Dots Spinner */
.spinner-pulse {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.pulse-dot {
  width: 1em;
  height: 1em;
  background: var(--color-primary);
  border-radius: 50%;
  animation: pulseDot 1.4s ease-in-out infinite both;
}

.pulse-dot-1 { animation-delay: -0.32s; }
.pulse-dot-2 { animation-delay: -0.16s; }
.pulse-dot-3 { animation-delay: 0s; }

@keyframes pulseDot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Circle Spinner */
.spinner-circle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-loader {
  width: 2em;
  height: 2em;
  border: 0.2em solid var(--color-gray-200);
  border-top: 0.2em solid var(--color-primary);
  border-radius: 50%;
  animation: circleRotate 1s linear infinite;
}

@keyframes circleRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Medical Cross Spinner */
.spinner-medical {
  display: flex;
  align-items: center;
  justify-content: center;
}

.medical-cross {
  color: var(--color-primary);
  animation: medicalPulse 2s ease-in-out infinite;
}

@keyframes medicalPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  25% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.8) rotate(180deg);
    opacity: 0.9;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 0.85;
  }
}

/* Message Styles */
.spinner-message {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
  margin: 0;
  line-height: var(--line-height-normal);
}

.spinner-message.text-primary { color: var(--color-primary); }
.spinner-message.text-secondary { color: var(--color-secondary); }
.spinner-message.text-white { color: var(--color-white); }
.spinner-message.text-gray { color: var(--color-gray-600); }

/* Overlay Message */
.spinner-overlay .spinner-message {
  color: var(--color-white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-spinner-container.spinner-center {
    padding: 1rem;
  }
  
  .spinner-sm { font-size: 0.875rem; }
  .spinner-md { font-size: 1.25rem; }
  .spinner-lg { font-size: 1.75rem; }
  .spinner-xl { font-size: 2.5rem; }
  
  .spinner-message {
    font-size: var(--font-size-xs);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .blood-drop,
  .heartbeat,
  .pulse-dot,
  .circle-loader,
  .medical-cross {
    animation: none;
  }
  
  .blood-drop,
  .heartbeat,
  .medical-cross {
    opacity: 0.8;
  }
  
  .pulse-dot {
    opacity: 1;
    transform: scale(1);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .pulse-dot,
  .circle-loader {
    border-width: 0.3em;
  }
  
  .spinner-message {
    font-weight: var(--font-weight-bold);
  }
}

/* Print Styles */
@media print {
  .modern-spinner-container {
    display: none;
  }
}
