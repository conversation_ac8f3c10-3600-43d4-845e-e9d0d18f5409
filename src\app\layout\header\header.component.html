<header class="modern-header">
    <nav class="navbar navbar-expand-lg sticky-top shadow-lg" role="navigation" aria-label="Main navigation">
        <div class="container-fluid px-4">
            <!-- Brand Section -->
            <a class="navbar-brand d-flex align-items-center" routerLink="/" aria-label="Blood Bank Management System Home">
                <div class="brand-logo-container">
                    <img [src]="ASSETS.IMAGES.LOGO" alt="Blood Bank Logo" class="brand-logo">
                </div>
                <div class="brand-text ms-3">
                    <h1 class="brand-title mb-0">LifeFlow</h1>
                    <p class="brand-subtitle mb-0">Blood Bank System</p>
                </div>
            </a>

            <!-- Mobile Toggle Button -->
            <button
                class="navbar-toggler border-0 p-2"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#navbarContent"
                aria-controls="navbarContent"
                aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon-custom">
                    <i class="bi bi-list fs-4"></i>
                </span>
            </button>

            <!-- Navigation Content -->
            <div class="collapse navbar-collapse" id="navbarContent">
                <!-- Main Navigation -->
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">
                            <i class="bi bi-house-door me-2"></i>
                            Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" href="#blood-info">
                            <i class="bi bi-droplet me-2"></i>
                            Blood Info
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" href="#donation">
                            <i class="bi bi-heart me-2"></i>
                            Donation
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" routerLink="/aboutus" routerLinkActive="active">
                            <i class="bi bi-info-circle me-2"></i>
                            About Us
                        </a>
                    </li>
                </ul>

                <!-- Action Buttons -->
                <div class="navbar-actions d-flex align-items-center gap-2">
                    <!-- Donate Now Button -->
                    <button
                        *ngIf="showDonationButton"
                        routerLink="/login"
                        class="btn btn-outline-light btn-action hover-lift"
                        type="button"
                        aria-label="Start donation process">
                        <i class="bi bi-heart-pulse me-2"></i>
                        Donate Now
                    </button>

                    <!-- Sign In/Out Button -->
                    <button
                        *ngIf="showSignUpButton"
                        routerLink="/login"
                        class="btn btn-light btn-action hover-lift"
                        type="button"
                        (click)="toggleSignIn()"
                        [attr.aria-label]="isSignedIn ? 'Sign out of account' : 'Sign up for account'">
                        <i class="bi" [ngClass]="isSignedIn ? 'bi-box-arrow-right' : 'bi-person-plus'" [class.me-2]="true"></i>
                        {{ isSignedIn ? 'Sign Out' : 'Sign Up' }}
                    </button>

                    <!-- User Profile Dropdown (when signed in) -->
                    <div *ngIf="isSignedIn" class="dropdown">
                        <button
                            class="btn btn-link text-white p-2"
                            type="button"
                            id="userDropdown"
                            data-bs-toggle="dropdown"
                            aria-expanded="false"
                            aria-label="User menu">
                            <i class="bi bi-person-circle fs-5"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" (click)="toggleSignIn()"><i class="bi bi-box-arrow-right me-2"></i>Sign Out</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb Navigation (for authenticated pages) -->
    <nav *ngIf="showBreadcrumb" class="breadcrumb-nav bg-gray-50 py-2" aria-label="Breadcrumb">
        <div class="container-fluid px-4">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a routerLink="/" class="text-decoration-none">
                        <i class="bi bi-house-door me-1"></i>
                        Home
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    {{ currentPageTitle }}
                </li>
            </ol>
        </div>
    </nav>
</header>
