<div class="modern-admin-tables">
  <!-- Tab Navigation -->
  <div class="table-tabs-container">
    <ul class="nav nav-tabs modern-tabs" id="adminTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button
          class="nav-link active"
          id="appointments-tab"
          data-bs-toggle="tab"
          data-bs-target="#appointments"
          type="button"
          role="tab"
          aria-controls="appointments"
          aria-selected="true">
          <i class="bi bi-calendar-check me-2"></i>
          Appointments
          <span class="badge bg-primary ms-2">{{ getTotalAppointments() }}</span>
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          id="blood-requests-tab"
          data-bs-toggle="tab"
          data-bs-target="#blood-requests"
          type="button"
          role="tab"
          aria-controls="blood-requests"
          aria-selected="false">
          <i class="bi bi-droplet-half me-2"></i>
          Blood Requests
          <span class="badge bg-warning ms-2">{{ getTotalBloodRequests() }}</span>
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          id="feedback-tab"
          data-bs-toggle="tab"
          data-bs-target="#feedback"
          type="button"
          role="tab"
          aria-controls="feedback"
          aria-selected="false">
          <i class="bi bi-chat-square-text me-2"></i>
          Feedback
          <span class="badge bg-info ms-2">{{ feedBackList?.length || 0 }}</span>
        </button>
      </li>
    </ul>
  </div>

  <!-- Tab Content -->
  <div class="tab-content modern-tab-content" id="adminTabsContent">
    <!-- Appointments Tab -->
    <div class="tab-pane fade show active" id="appointments" role="tabpanel" aria-labelledby="appointments-tab">
      <!-- Appointment Sub-tabs -->
      <div class="sub-tabs-container">
        <ul class="nav nav-pills sub-tabs" id="appointmentSubTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link active"
              id="pending-appointments-tab"
              data-bs-toggle="pill"
              data-bs-target="#pending-appointments"
              type="button"
              role="tab">
              <i class="bi bi-clock-history me-2"></i>
              Pending
              <span class="badge bg-warning ms-2">{{ appointments?.length || 0 }}</span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="approved-appointments-tab"
              data-bs-toggle="pill"
              data-bs-target="#approved-appointments"
              type="button"
              role="tab">
              <i class="bi bi-check-circle me-2"></i>
              Approved
              <span class="badge bg-success ms-2">{{ appointmentsApproved?.length || 0 }}</span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="canceled-appointments-tab"
              data-bs-toggle="pill"
              data-bs-target="#canceled-appointments"
              type="button"
              role="tab">
              <i class="bi bi-x-circle me-2"></i>
              Canceled
              <span class="badge bg-danger ms-2">{{ canceledAppointmentList?.length || 0 }}</span>
            </button>
          </li>
        </ul>
      </div>

      <div class="tab-content sub-tab-content">
        <!-- Pending Appointments -->
        <div class="tab-pane fade show active" id="pending-appointments" role="tabpanel">
          <div class="table-header">
            <h3 class="table-title">
              <i class="bi bi-clock-history text-warning me-2"></i>
              Pending Appointments
            </h3>
            <div class="table-actions">
              <div class="search-box">
                <i class="bi bi-search"></i>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Search appointments..."
                  [(ngModel)]="searchTerm"
                  (input)="onSearch()">
              </div>
            </div>
          </div>

          <div class="modern-table-container">
            <div class="table-responsive">
              <table class="table modern-table">
                <thead>
                  <tr>
                    <th scope="col">#</th>
                    <th scope="col">Appointment ID</th>
                    <th scope="col">Donor ID</th>
                    <th scope="col">Blood Type</th>
                    <th scope="col">Donation Date</th>
                    <th scope="col">Location</th>
                    <th scope="col">Remarks</th>
                    <th scope="col">Status</th>
                    <th scope="col" class="text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let appointment of getFilteredAppointments(); let i = index" class="table-row">
                    <td class="row-number">{{ i + 1 }}</td>
                    <td class="appointment-id">
                      <span class="id-badge">{{ appointment.appointmentID }}</span>
                    </td>
                    <td class="donor-id">
                      <span class="id-badge">{{ appointment.donarID }}</span>
                    </td>
                    <td class="blood-type">
                      <span class="blood-type-badge" [ngClass]="getBloodTypeClass(appointment.bloodType)">
                        <i class="bi bi-droplet-fill me-1"></i>
                        {{ appointment.bloodType }}
                      </span>
                    </td>
                    <td class="donation-date">
                      <div class="date-info">
                        <span class="date">{{ formatDate(appointment.preferDate) }}</span>
                        <span class="time-ago">{{ getTimeAgo(appointment.preferDate) }}</span>
                      </div>
                    </td>
                    <td class="location">
                      <div class="location-info">
                        <i class="bi bi-geo-alt text-muted me-1"></i>
                        <span>{{ appointment.address }}</span>
                      </div>
                    </td>
                    <td class="remarks">
                      <span class="remarks-text" [title]="appointment.remarks">
                        {{ truncateText(appointment.remarks, 30) }}
                      </span>
                    </td>
                    <td class="status">
                      <span class="status-badge" [ngClass]="getStatusClass(appointment.status)">
                        <i class="bi" [ngClass]="getStatusIcon(appointment.status)"></i>
                        {{ appointment.status }}
                      </span>
                    </td>
                    <td class="actions text-center">
                      <div class="action-buttons">
                        <button
                          class="btn btn-success btn-sm hover-lift me-2"
                          (click)="approve(appointment)"
                          [attr.aria-label]="'Approve appointment ' + appointment.appointmentID">
                          <i class="bi bi-check-circle"></i>
                        </button>
                        <button
                          class="btn btn-danger btn-sm hover-lift"
                          (click)="cancel(appointment)"
                          [attr.aria-label]="'Cancel appointment ' + appointment.appointmentID">
                          <i class="bi bi-x-circle"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="getFilteredAppointments().length === 0" class="no-data-row">
                    <td colspan="9" class="text-center py-5">
                      <div class="no-data-message">
                        <i class="bi bi-inbox display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No pending appointments found</h5>
                        <p class="text-muted">All appointments have been processed or no new appointments are available.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- Approved Appointments -->
        <div class="tab-pane fade" id="approved-appointments" role="tabpanel">
          <div class="table-header">
            <h3 class="table-title">
              <i class="bi bi-check-circle text-success me-2"></i>
              Approved Appointments
            </h3>
          </div>

          <div class="modern-table-container">
            <div class="table-responsive">
              <table class="table modern-table">
                <thead>
                  <tr>
                    <th scope="col">#</th>
                    <th scope="col">Appointment ID</th>
                    <th scope="col">Donor ID</th>
                    <th scope="col">Blood Type</th>
                    <th scope="col">Donation Date</th>
                    <th scope="col">Location</th>
                    <th scope="col">Remarks</th>
                    <th scope="col">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let appointment of appointmentsApproved; let i = index" class="table-row">
                    <td class="row-number">{{ i + 1 }}</td>
                    <td class="appointment-id">
                      <span class="id-badge">{{ appointment.appointmentID }}</span>
                    </td>
                    <td class="donor-id">
                      <span class="id-badge">{{ appointment.donarID }}</span>
                    </td>
                    <td class="blood-type">
                      <span class="blood-type-badge" [ngClass]="getBloodTypeClass(appointment.bloodType)">
                        <i class="bi bi-droplet-fill me-1"></i>
                        {{ appointment.bloodType }}
                      </span>
                    </td>
                    <td class="donation-date">
                      <div class="date-info">
                        <span class="date">{{ formatDate(appointment.preferDate) }}</span>
                        <span class="time-ago">{{ getTimeAgo(appointment.preferDate) }}</span>
                      </div>
                    </td>
                    <td class="location">
                      <div class="location-info">
                        <i class="bi bi-geo-alt text-muted me-1"></i>
                        <span>{{ appointment.address }}</span>
                      </div>
                    </td>
                    <td class="remarks">
                      <span class="remarks-text" [title]="appointment.remarks">
                        {{ truncateText(appointment.remarks, 30) }}
                      </span>
                    </td>
                    <td class="status">
                      <span class="status-badge status-approved">
                        <i class="bi bi-check-circle"></i>
                        {{ appointment.status }}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="appointmentsApproved?.length === 0" class="no-data-row">
                    <td colspan="8" class="text-center py-5">
                      <div class="no-data-message">
                        <i class="bi bi-check-circle display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No approved appointments</h5>
                        <p class="text-muted">Approved appointments will appear here.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Canceled Appointments -->
        <div class="tab-pane fade" id="canceled-appointments" role="tabpanel">
          <div class="table-header">
            <h3 class="table-title">
              <i class="bi bi-x-circle text-danger me-2"></i>
              Canceled Appointments
            </h3>
          </div>

          <div class="modern-table-container">
            <div class="table-responsive">
              <table class="table modern-table">
                <thead>
                  <tr>
                    <th scope="col">#</th>
                    <th scope="col">Appointment ID</th>
                    <th scope="col">Donor ID</th>
                    <th scope="col">Blood Type</th>
                    <th scope="col">Donation Date</th>
                    <th scope="col">Location</th>
                    <th scope="col">Remarks</th>
                    <th scope="col">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let appointment of canceledAppointmentList; let i = index" class="table-row">
                    <td class="row-number">{{ i + 1 }}</td>
                    <td class="appointment-id">
                      <span class="id-badge">{{ appointment.appointmentID }}</span>
                    </td>
                    <td class="donor-id">
                      <span class="id-badge">{{ appointment.donarID }}</span>
                    </td>
                    <td class="blood-type">
                      <span class="blood-type-badge" [ngClass]="getBloodTypeClass(appointment.bloodType)">
                        <i class="bi bi-droplet-fill me-1"></i>
                        {{ appointment.bloodType }}
                      </span>
                    </td>
                    <td class="donation-date">
                      <div class="date-info">
                        <span class="date">{{ formatDate(appointment.preferDate) }}</span>
                        <span class="time-ago">{{ getTimeAgo(appointment.preferDate) }}</span>
                      </div>
                    </td>
                    <td class="location">
                      <div class="location-info">
                        <i class="bi bi-geo-alt text-muted me-1"></i>
                        <span>{{ appointment.address }}</span>
                      </div>
                    </td>
                    <td class="remarks">
                      <span class="remarks-text" [title]="appointment.remarks">
                        {{ truncateText(appointment.remarks, 30) }}
                      </span>
                    </td>
                    <td class="status">
                      <span class="status-badge status-cancelled">
                        <i class="bi bi-x-circle"></i>
                        {{ appointment.status }}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="canceledAppointmentList?.length === 0" class="no-data-row">
                    <td colspan="8" class="text-center py-5">
                      <div class="no-data-message">
                        <i class="bi bi-x-circle display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No canceled appointments</h5>
                        <p class="text-muted">Canceled appointments will appear here.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Blood Requests Tab -->
    <div class="tab-pane fade" id="blood-requests" role="tabpanel" aria-labelledby="blood-requests-tab">
      <!-- Hospital Manager Header -->
      <div class="section-header-with-action">
        <div class="section-info">
          <h3 class="section-title">
            <i class="bi bi-building text-primary me-2"></i>
            Hospital Management
          </h3>
          <p class="section-subtitle">Manage blood requests from partner hospitals</p>
        </div>
        <div class="section-actions">
          <button class="btn btn-primary hover-lift" (click)="navigate()">
            <i class="bi bi-plus-circle me-2"></i>
            Add New Hospital
          </button>
        </div>
      </div>

      <!-- Blood Request Sub-tabs -->
      <div class="sub-tabs-container">
        <ul class="nav nav-pills sub-tabs" id="bloodRequestSubTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link active"
              id="pending-requests-tab"
              data-bs-toggle="pill"
              data-bs-target="#pending-requests"
              type="button"
              role="tab">
              <i class="bi bi-clock-history me-2"></i>
              Pending Requests
              <span class="badge bg-warning ms-2">{{ bloodRequestList?.length || 0 }}</span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="completed-requests-tab"
              data-bs-toggle="pill"
              data-bs-target="#completed-requests"
              type="button"
              role="tab">
              <i class="bi bi-check-circle me-2"></i>
              Completed Requests
              <span class="badge bg-success ms-2">{{ completedBloodRequest?.length || 0 }}</span>
            </button>
          </li>
        </ul>
      </div>

      <div class="tab-content sub-tab-content">
        <!-- Pending Blood Requests -->
        <div class="tab-pane fade show active" id="pending-requests" role="tabpanel">
          <div class="table-header">
            <h3 class="table-title">
              <i class="bi bi-droplet-half text-warning me-2"></i>
              Pending Blood Requests
            </h3>
            <div class="table-actions">
              <div class="search-box">
                <i class="bi bi-search"></i>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Search requests..."
                  [(ngModel)]="bloodRequestSearchTerm"
                  (input)="onBloodRequestSearch()">
              </div>
            </div>
          </div>

          <div class="modern-table-container">
            <div class="table-responsive">
              <table class="table modern-table">
                <thead>
                  <tr>
                    <th scope="col">#</th>
                    <th scope="col">Hospital ID</th>
                    <th scope="col">Hospital Name</th>
                    <th scope="col">Blood Type</th>
                    <th scope="col">Amount</th>
                    <th scope="col">Contact</th>
                    <th scope="col">Message</th>
                    <th scope="col">Urgency</th>
                    <th scope="col">Status</th>
                    <th scope="col" class="text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let request of getFilteredBloodRequests(); let i = index" class="table-row">
                    <td class="row-number">{{ i + 1 }}</td>
                    <td class="hospital-id">
                      <span class="id-badge">{{ request.hospitalID }}</span>
                    </td>
                    <td class="hospital-name">
                      <div class="hospital-info">
                        <i class="bi bi-building text-muted me-2"></i>
                        <span class="fw-semibold">{{ request.name }}</span>
                      </div>
                    </td>
                    <td class="blood-type">
                      <span class="blood-type-badge" [ngClass]="getBloodTypeClass(request.bloodType)">
                        <i class="bi bi-droplet-fill me-1"></i>
                        {{ request.bloodType }}
                      </span>
                    </td>
                    <td class="amount">
                      <div class="amount-info">
                        <span class="amount-value">{{ request.amount }}</span>
                        <span class="amount-unit">units</span>
                      </div>
                    </td>
                    <td class="contact">
                      <div class="contact-info">
                        <i class="bi bi-telephone text-muted me-1"></i>
                        <span>{{ request.contactNumber }}</span>
                      </div>
                    </td>
                    <td class="message">
                      <span class="message-text" [title]="request.massage">
                        {{ truncateText(request.massage, 25) }}
                      </span>
                    </td>
                    <td class="urgency">
                      <span class="urgency-badge" [ngClass]="getUrgencyClass(request.type)">
                        <i class="bi" [ngClass]="getUrgencyIcon(request.type)"></i>
                        {{ request.type }}
                      </span>
                    </td>
                    <td class="status">
                      <span class="status-badge" [ngClass]="getStatusClass(request.status)">
                        <i class="bi" [ngClass]="getStatusIcon(request.status)"></i>
                        {{ request.status }}
                      </span>
                    </td>
                    <td class="actions text-center">
                      <div class="action-buttons">
                        <button
                          class="btn btn-success btn-sm hover-lift me-2"
                          (click)="approveBlood(request)"
                          [attr.aria-label]="'Approve blood request from ' + request.name">
                          <i class="bi bi-check-circle"></i>
                        </button>
                        <button
                          class="btn btn-danger btn-sm hover-lift"
                          (click)="cancelBloodRequest(request)"
                          [attr.aria-label]="'Cancel blood request from ' + request.name">
                          <i class="bi bi-x-circle"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="getFilteredBloodRequests().length === 0" class="no-data-row">
                    <td colspan="10" class="text-center py-5">
                      <div class="no-data-message">
                        <i class="bi bi-droplet display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No pending blood requests</h5>
                        <p class="text-muted">All blood requests have been processed or no new requests are available.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- Completed Blood Requests -->
        <div class="tab-pane fade" id="completed-requests" role="tabpanel">
          <div class="table-header">
            <h3 class="table-title">
              <i class="bi bi-check-circle text-success me-2"></i>
              Completed Blood Requests
            </h3>
          </div>

          <div class="modern-table-container">
            <div class="table-responsive">
              <table class="table modern-table">
                <thead>
                  <tr>
                    <th scope="col">#</th>
                    <th scope="col">Hospital ID</th>
                    <th scope="col">Hospital Name</th>
                    <th scope="col">Blood Type</th>
                    <th scope="col">Amount</th>
                    <th scope="col">Contact</th>
                    <th scope="col">Message</th>
                    <th scope="col">Urgency</th>
                    <th scope="col">Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let request of completedBloodRequest; let i = index" class="table-row">
                    <td class="row-number">{{ i + 1 }}</td>
                    <td class="hospital-id">
                      <span class="id-badge">{{ request.hospitalID }}</span>
                    </td>
                    <td class="hospital-name">
                      <div class="hospital-info">
                        <i class="bi bi-building text-muted me-2"></i>
                        <span class="fw-semibold">{{ request.name }}</span>
                      </div>
                    </td>
                    <td class="blood-type">
                      <span class="blood-type-badge" [ngClass]="getBloodTypeClass(request.bloodType)">
                        <i class="bi bi-droplet-fill me-1"></i>
                        {{ request.bloodType }}
                      </span>
                    </td>
                    <td class="amount">
                      <div class="amount-info">
                        <span class="amount-value">{{ request.amount }}</span>
                        <span class="amount-unit">units</span>
                      </div>
                    </td>
                    <td class="contact">
                      <div class="contact-info">
                        <i class="bi bi-telephone text-muted me-1"></i>
                        <span>{{ request.contactNumber }}</span>
                      </div>
                    </td>
                    <td class="message">
                      <span class="message-text" [title]="request.massage">
                        {{ truncateText(request.massage, 25) }}
                      </span>
                    </td>
                    <td class="urgency">
                      <span class="urgency-badge" [ngClass]="getUrgencyClass(request.type)">
                        <i class="bi" [ngClass]="getUrgencyIcon(request.type)"></i>
                        {{ request.type }}
                      </span>
                    </td>
                    <td class="status">
                      <span class="status-badge status-completed">
                        <i class="bi bi-check-circle"></i>
                        {{ request.status }}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="completedBloodRequest?.length === 0" class="no-data-row">
                    <td colspan="9" class="text-center py-5">
                      <div class="no-data-message">
                        <i class="bi bi-check-circle display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No completed requests</h5>
                        <p class="text-muted">Completed blood requests will appear here.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feedback Tab -->
    <div class="tab-pane fade" id="feedback" role="tabpanel" aria-labelledby="feedback-tab">
      <div class="table-header">
        <h3 class="table-title">
          <i class="bi bi-chat-square-text text-info me-2"></i>
          User Feedback
        </h3>
        <div class="table-actions">
          <div class="search-box">
            <i class="bi bi-search"></i>
            <input
              type="text"
              class="form-control"
              placeholder="Search feedback..."
              [(ngModel)]="feedbackSearchTerm"
              (input)="onFeedbackSearch()">
          </div>
        </div>
      </div>

      <div class="modern-table-container">
        <div class="table-responsive">
          <table class="table modern-table">
            <thead>
              <tr>
                <th scope="col">#</th>
                <th scope="col">Feedback ID</th>
                <th scope="col">Message</th>
                <th scope="col">Date</th>
                <th scope="col">Time</th>
                <th scope="col">Rating</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let feedback of getFilteredFeedback(); let i = index" class="table-row">
                <td class="row-number">{{ i + 1 }}</td>
                <td class="feedback-id">
                  <span class="id-badge">{{ feedback.feedbackID }}</span>
                </td>
                <td class="feedback-text">
                  <div class="feedback-content">
                    <p class="feedback-message" [title]="feedback.text">
                      {{ truncateText(feedback.text, 50) }}
                    </p>
                  </div>
                </td>
                <td class="feedback-date">
                  <div class="date-info">
                    <span class="date">{{ formatDate(feedback.date) }}</span>
                  </div>
                </td>
                <td class="feedback-time">
                  <div class="time-info">
                    <i class="bi bi-clock text-muted me-1"></i>
                    <span>{{ feedback.time }}</span>
                  </div>
                </td>
                <td class="feedback-rating">
                  <div class="rating-stars">
                    <i class="bi bi-star-fill text-warning" *ngFor="let star of getStarArray(feedback.rating || 5)"></i>
                    <span class="rating-value ms-2">{{ feedback.rating || 5 }}/5</span>
                  </div>
                </td>
              </tr>
              <tr *ngIf="getFilteredFeedback().length === 0" class="no-data-row">
                <td colspan="6" class="text-center py-5">
                  <div class="no-data-message">
                    <i class="bi bi-chat-square-text display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">No feedback available</h5>
                    <p class="text-muted">User feedback will appear here when submitted.</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
