/* Modern Admin Dashboard Styles */
.modern-admin-dashboard {
  background: var(--color-background);
  min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  padding: 2rem 0;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 2;
}

.dashboard-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: 0.5rem;
  line-height: var(--line-height-tight);
}

.dashboard-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin-bottom: 0;
  line-height: var(--line-height-normal);
}

.header-actions {
  position: relative;
  z-index: 2;
}

.header-actions .btn {
  border-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

/* Dashboard Sections */
.dashboard-section {
  padding: 3rem 0;
}

.dashboard-section.bg-gray-50 {
  background: var(--color-gray-50);
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  margin-bottom: 0;
}

/* Blood Inventory Grid */
.blood-inventory-grid {
  margin-bottom: 2rem;
}

/* Quick Stats */
.quick-stats {
  margin-top: 3rem;
}

.stat-card {
  background: var(--color-white);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 4rem;
  height: 4rem;
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--color-white);
  flex-shrink: 0;
}

.stat-icon.bg-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.stat-icon.bg-success {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-accent-dark) 100%);
}

.stat-icon.bg-info {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.stat-icon.bg-warning {
  background: linear-gradient(135deg, var(--color-warning) 0%, #b45309 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin-bottom: 0;
  font-weight: var(--font-weight-medium);
}

/* Admin Profile Card */
.admin-profile-card {
  background: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
}

.profile-header {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
  border-bottom: 1px solid var(--color-gray-200);
}

.profile-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 100px;
  height: 100px;
  border-radius: var(--border-radius-full);
  border: 4px solid var(--color-white);
  box-shadow: var(--shadow-md);
  object-fit: cover;
}

.avatar-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 2rem;
  height: 2rem;
  background: var(--color-success);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-sm);
  border: 3px solid var(--color-white);
}

.profile-info {
  flex: 1;
}

.admin-id-badge {
  display: inline-flex;
  align-items: center;
  background: var(--color-primary-100);
  color: var(--color-primary-800);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: 1rem;
}

.profile-name {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  color: var(--color-gray-600);
  font-size: var(--font-size-base);
}

.profile-actions {
  padding: 2rem;
  background: var(--color-gray-50);
  text-align: center;
}

/* Appointment Manager */
.appointment-manager-container {
  background: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  padding: 2rem;
  border: 1px solid var(--color-gray-200);
}

/* Activity Cards */
.activity-card {
  background: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
}

.activity-header {
  background: var(--color-gray-50);
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-gray-200);
}

.activity-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.activity-list {
  padding: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--color-gray-100);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  color: var(--color-white);
  flex-shrink: 0;
}

.activity-icon.bg-success {
  background: var(--color-success);
}

.activity-icon.bg-warning {
  background: var(--color-warning);
}

.activity-icon.bg-danger {
  background: var(--color-error);
}

.activity-icon.bg-info {
  background: var(--color-info);
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
  margin-bottom: 0.25rem;
  font-weight: var(--font-weight-medium);
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

/* Responsive Design */
@media (max-width: 1199.98px) {
  .dashboard-title {
    font-size: var(--font-size-3xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-icon {
    width: 3rem;
    height: 3rem;
    font-size: var(--font-size-lg);
  }

  .stat-number {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 991.98px) {
  .dashboard-header {
    padding: 1.5rem 0;
  }

  .dashboard-title {
    font-size: var(--font-size-2xl);
    text-align: center;
  }

  .dashboard-subtitle {
    text-align: center;
  }

  .header-actions {
    text-align: center;
    margin-top: 1rem;
  }

  .section-header {
    margin-bottom: 2rem;
  }

  .section-title {
    font-size: var(--font-size-xl);
    flex-direction: column;
    gap: 0.5rem;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .profile-details {
    align-items: center;
  }
}

@media (max-width: 767.98px) {
  .dashboard-section {
    padding: 2rem 0;
  }

  .dashboard-title {
    font-size: var(--font-size-xl);
  }

  .section-title {
    font-size: var(--font-size-lg);
  }

  .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .stat-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: var(--font-size-base);
  }

  .stat-number {
    font-size: var(--font-size-xl);
  }

  .admin-profile-card {
    margin: 0 1rem;
  }

  .profile-header {
    padding: 1.5rem;
  }

  .avatar-image {
    width: 80px;
    height: 80px;
  }

  .profile-name {
    font-size: var(--font-size-xl);
  }

  .profile-actions {
    padding: 1.5rem;
  }

  .profile-actions .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .activity-card {
    margin-bottom: 1rem;
  }

  .activity-header {
    padding: 1rem;
  }

  .activity-list {
    padding: 1rem;
  }

  .activity-item {
    padding: 0.75rem 0;
  }
}

@media (max-width: 575.98px) {
  .dashboard-header {
    padding: 1rem 0;
  }

  .dashboard-title {
    font-size: var(--font-size-lg);
  }

  .dashboard-subtitle {
    font-size: var(--font-size-base);
  }

  .header-actions .btn {
    font-size: var(--font-size-sm);
    padding: 0.5rem 1rem;
    margin: 0.25rem;
  }

  .section-title {
    font-size: var(--font-size-base);
  }

  .section-subtitle {
    font-size: var(--font-size-sm);
  }

  .stat-number {
    font-size: var(--font-size-lg);
  }

  .stat-label {
    font-size: var(--font-size-sm);
  }

  .avatar-image {
    width: 60px;
    height: 60px;
  }

  .avatar-badge {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.625rem;
  }

  .admin-id-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .profile-name {
    font-size: var(--font-size-lg);
  }

  .detail-item {
    font-size: var(--font-size-sm);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .stat-card,
  .admin-profile-card,
  .activity-card {
    transition: none;
  }

  .stat-card:hover,
  .admin-profile-card:hover,
  .activity-card:hover {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .dashboard-header {
    border-bottom: 2px solid var(--color-white);
  }

  .stat-card,
  .admin-profile-card,
  .activity-card {
    border: 2px solid var(--color-gray-800);
  }

  .section-title,
  .dashboard-title {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
}

/* Print Styles */
@media print {
  .dashboard-header {
    background: none !important;
    color: var(--color-gray-900) !important;
  }

  .header-actions,
  .profile-actions {
    display: none;
  }

  .stat-card,
  .admin-profile-card,
  .activity-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--color-gray-400);
  }

  .dashboard-section {
    padding: 1rem 0;
  }
}
