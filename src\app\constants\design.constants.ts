/**
 * Design System Constants
 * Centralized design tokens for consistent UI across the Blood Bank Management System
 */

export const DESIGN_TOKENS = {
  // Color Palette
  COLORS: {
    // Primary - Medical Red
    PRIMARY: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626', // Main primary
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },

    // Secondary - Medical Blue
    SECONDARY: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af', // Main secondary
      900: '#1e3a8a',
    },

    // Accent - Medical Green
    ACCENT: {
      50: '#ecfdf5',
      100: '#d1fae5',
      200: '#a7f3d0',
      300: '#6ee7b7',
      400: '#34d399',
      500: '#10b981',
      600: '#059669', // Main accent
      700: '#047857',
      800: '#065f46',
      900: '#064e3b',
    },

    // Neutral Grays
    GRAY: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },

    // Status Colors
    STATUS: {
      SUCCESS: '#059669',
      WARNING: '#d97706',
      ERROR: '#dc2626',
      INFO: '#2563eb',
    },

    // Special Colors
    WHITE: '#ffffff',
    BLACK: '#000000',
  },

  // Typography
  TYPOGRAPHY: {
    FONT_FAMILIES: {
      PRIMARY: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
      SECONDARY: "'Poppins', sans-serif",
      MONO: "'JetBrains Mono', 'Fira Code', monospace",
    },

    FONT_SIZES: {
      XS: '0.75rem',    // 12px
      SM: '0.875rem',   // 14px
      BASE: '1rem',     // 16px
      LG: '1.125rem',   // 18px
      XL: '1.25rem',    // 20px
      '2XL': '1.5rem',  // 24px
      '3XL': '1.875rem', // 30px
      '4XL': '2.25rem', // 36px
      '5XL': '3rem',    // 48px
      '6XL': '3.75rem', // 60px
    },

    FONT_WEIGHTS: {
      LIGHT: 300,
      NORMAL: 400,
      MEDIUM: 500,
      SEMIBOLD: 600,
      BOLD: 700,
      EXTRABOLD: 800,
    },

    LINE_HEIGHTS: {
      TIGHT: 1.25,
      NORMAL: 1.5,
      RELAXED: 1.75,
    },
  },

  // Spacing Scale
  SPACING: {
    XS: '0.25rem',   // 4px
    SM: '0.5rem',    // 8px
    MD: '1rem',      // 16px
    LG: '1.5rem',    // 24px
    XL: '2rem',      // 32px
    '2XL': '3rem',   // 48px
    '3XL': '4rem',   // 64px
    '4XL': '6rem',   // 96px
  },

  // Border Radius
  BORDER_RADIUS: {
    NONE: '0',
    SM: '0.25rem',   // 4px
    MD: '0.375rem',  // 6px
    LG: '0.5rem',    // 8px
    XL: '0.75rem',   // 12px
    '2XL': '1rem',   // 16px
    FULL: '9999px',
  },

  // Shadows
  SHADOWS: {
    SM: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    MD: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    LG: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    XL: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    INNER: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  },

  // Transitions
  TRANSITIONS: {
    FAST: '150ms ease-in-out',
    NORMAL: '300ms ease-in-out',
    SLOW: '500ms ease-in-out',
  },

  // Breakpoints
  BREAKPOINTS: {
    SM: '576px',
    MD: '768px',
    LG: '992px',
    XL: '1200px',
    XXL: '1400px',
  },

  // Z-Index Scale
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
  },
} as const;

// CSS Class Utilities
export const CSS_CLASSES = {
  // Text Colors
  TEXT: {
    PRIMARY: 'text-primary',
    SECONDARY: 'text-secondary',
    ACCENT: 'text-accent',
    SUCCESS: 'text-success',
    WARNING: 'text-warning',
    ERROR: 'text-error',
    INFO: 'text-info',
    MUTED: 'text-gray-500',
  },

  // Background Colors
  BACKGROUND: {
    PRIMARY: 'bg-primary',
    SECONDARY: 'bg-secondary',
    ACCENT: 'bg-accent',
    SUCCESS: 'bg-success',
    WARNING: 'bg-warning',
    ERROR: 'bg-error',
    INFO: 'bg-info',
    LIGHT: 'bg-gray-50',
    WHITE: 'bg-white',
  },

  // Gradients
  GRADIENT: {
    PRIMARY: 'gradient-primary',
    SECONDARY: 'gradient-secondary',
    ACCENT: 'gradient-accent',
    MEDICAL: 'gradient-medical',
  },

  // Hover Effects
  HOVER: {
    LIFT: 'hover-lift',
    SCALE: 'hover-scale',
    GLOW: 'hover-glow',
  },

  // Status Indicators
  STATUS: {
    PENDING: 'status-pending',
    APPROVED: 'status-approved',
    COMPLETED: 'status-completed',
    REJECTED: 'status-rejected',
    CANCELLED: 'status-cancelled',
  },

  // Urgency Levels
  URGENCY: {
    LOW: 'urgency-low',
    MEDIUM: 'urgency-medium',
    HIGH: 'urgency-high',
    CRITICAL: 'urgency-critical',
  },

  // Blood Type Badges
  BLOOD_TYPE: {
    A: 'blood-type-badge type-a',
    B: 'blood-type-badge type-b',
    AB: 'blood-type-badge type-ab',
    O: 'blood-type-badge type-o',
  },

  // Shadows
  SHADOW: {
    SM: 'shadow-sm',
    MD: 'shadow-md',
    LG: 'shadow-lg',
    XL: 'shadow-xl',
    NONE: 'shadow-none',
  },

  // Border Radius
  ROUNDED: {
    NONE: 'rounded-none',
    SM: 'rounded-sm',
    MD: 'rounded-md',
    LG: 'rounded-lg',
    XL: 'rounded-xl',
    '2XL': 'rounded-2xl',
    FULL: 'rounded-full',
  },

  // Transitions
  TRANSITION: {
    FAST: 'transition-fast',
    NORMAL: 'transition-normal',
    SLOW: 'transition-slow',
  },
} as const;

// Helper Functions
export const getStatusClass = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    PENDING: CSS_CLASSES.STATUS.PENDING,
    APPROVED: CSS_CLASSES.STATUS.APPROVED,
    COMPLETED: CSS_CLASSES.STATUS.COMPLETED,
    REJECTED: CSS_CLASSES.STATUS.REJECTED,
    CANCELLED: CSS_CLASSES.STATUS.CANCELLED,
  };
  return statusMap[status] || CSS_CLASSES.STATUS.PENDING;
};

export const getUrgencyClass = (urgency: string): string => {
  const urgencyMap: { [key: string]: string } = {
    LOW: CSS_CLASSES.URGENCY.LOW,
    MEDIUM: CSS_CLASSES.URGENCY.MEDIUM,
    HIGH: CSS_CLASSES.URGENCY.HIGH,
    CRITICAL: CSS_CLASSES.URGENCY.CRITICAL,
  };
  return urgencyMap[urgency] || CSS_CLASSES.URGENCY.LOW;
};

export const getBloodTypeClass = (bloodType: string): string => {
  const type = bloodType.replace(/[+-]/, '').toUpperCase();
  const bloodTypeMap: { [key: string]: string } = {
    A: CSS_CLASSES.BLOOD_TYPE.A,
    B: CSS_CLASSES.BLOOD_TYPE.B,
    AB: CSS_CLASSES.BLOOD_TYPE.AB,
    O: CSS_CLASSES.BLOOD_TYPE.O,
  };
  return bloodTypeMap[type] || CSS_CLASSES.BLOOD_TYPE.O;
};
