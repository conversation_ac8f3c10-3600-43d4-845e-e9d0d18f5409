import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, state, style, transition, animate } from '@angular/animations';

export interface ToastConfig {
  id?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  dismissible?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

@Component({
  selector: 'app-toast',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="toast-container" 
      [ngClass]="positionClass"
      [@slideIn]="isVisible ? 'in' : 'out'"
      (@slideIn.done)="onAnimationDone($event)">
      
      <div class="toast" [ngClass]="toastClasses" role="alert" aria-live="polite">
        <!-- Toast Icon -->
        <div class="toast-icon">
          <i class="bi" [ngClass]="iconClass"></i>
        </div>

        <!-- Toast Content -->
        <div class="toast-content">
          <div *ngIf="config.title" class="toast-title">{{ config.title }}</div>
          <div class="toast-message">{{ config.message }}</div>
        </div>

        <!-- Dismiss Button -->
        <button 
          *ngIf="config.dismissible !== false" 
          type="button" 
          class="toast-dismiss"
          (click)="dismiss()"
          aria-label="Close notification">
          <i class="bi bi-x"></i>
        </button>

        <!-- Progress Bar -->
        <div *ngIf="showProgress" class="toast-progress">
          <div class="toast-progress-bar" [style.animation-duration.ms]="config.duration"></div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./toast.component.css'],
  animations: [
    trigger('slideIn', [
      state('in', style({ transform: 'translateX(0)', opacity: 1 })),
      state('out', style({ transform: 'translateX(100%)', opacity: 0 })),
      transition('out => in', animate('300ms ease-out')),
      transition('in => out', animate('300ms ease-in'))
    ])
  ]
})
export class ToastComponent implements OnInit, OnDestroy {
  @Input() config!: ToastConfig;
  @Output() dismissed = new EventEmitter<string>();

  isVisible = false;
  showProgress = false;
  private timeoutId?: number;

  ngOnInit(): void {
    // Set default values
    this.config = {
      duration: 5000,
      dismissible: true,
      position: 'top-right',
      ...this.config
    };

    this.showProgress = (this.config.duration || 0) > 0;
    
    // Show toast
    setTimeout(() => {
      this.isVisible = true;
    }, 100);

    // Auto dismiss
    if (this.config.duration && this.config.duration > 0) {
      this.timeoutId = window.setTimeout(() => {
        this.dismiss();
      }, this.config.duration);
    }
  }

  ngOnDestroy(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  dismiss(): void {
    this.isVisible = false;
  }

  onAnimationDone(event: any): void {
    if (event.toState === 'out') {
      this.dismissed.emit(this.config.id);
    }
  }

  get positionClass(): string {
    return `toast-${this.config.position || 'top-right'}`;
  }

  get toastClasses(): string {
    return `toast-${this.config.type}`;
  }

  get iconClass(): string {
    const iconMap = {
      success: 'bi-check-circle-fill',
      error: 'bi-x-circle-fill',
      warning: 'bi-exclamation-triangle-fill',
      info: 'bi-info-circle-fill'
    };
    return iconMap[this.config.type];
  }
}
