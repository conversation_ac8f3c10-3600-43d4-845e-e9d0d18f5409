export const APP_CONSTANTS = {
  // Application info
  APP_NAME: 'Blood Bank Management System',
  APP_VERSION: '1.0.0',

  // Local storage keys
  STORAGE_KEYS: {
    USER_TOKEN: 'userToken',
    USER_DATA: 'userData',
    THEME: 'theme',
  },

  // User types
  USER_TYPES: {
    ADMIN: 'ADMIN',
    DONOR: 'DONAR', // Keep original spelling for API compatibility
    HOSPITAL: 'HOSPITAL',
  } as const,

  // Blood types
  BLOOD_TYPES: [
    'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
  ] as const,

  // Status types
  STATUS: {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    COMPLETED: 'COMPLETED',
    REJECTED: 'REJECTED',
    CANCELLED: 'CANCELLED',
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
  } as const,

  // Urgency levels
  URGENCY_LEVELS: {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
    CRITICAL: 'CRITICAL',
  } as const,

  // Routes
  ROUTES: {
    HOME: '',
    LOGIN: 'login',
    REGISTER: 'DonarReg',
    DONOR_HOME: 'Donar-Home',
    ADMIN: 'Admin',
    HOSPITAL: 'Hospital',
    ADD_HOSPITAL: 'add-Hospital',
    ABOUT: 'aboutus',
  } as const,

  // Validation
  VALIDATION: {
    MIN_AGE: 18,
    MAX_AGE: 65,
    MIN_WEIGHT: 50, // kg
    MIN_PASSWORD_LENGTH: 6,
    PHONE_PATTERN: /^[0-9]{10}$/,
    EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  } as const,

  // Design System
  DESIGN: {
    // Color Palette - Medical Theme
    COLORS: {
      // Primary Colors
      PRIMARY: '#dc2626', // Medical red
      PRIMARY_LIGHT: '#ef4444',
      PRIMARY_DARK: '#b91c1c',
      PRIMARY_50: '#fef2f2',
      PRIMARY_100: '#fee2e2',
      PRIMARY_200: '#fecaca',
      PRIMARY_500: '#ef4444',
      PRIMARY_600: '#dc2626',
      PRIMARY_700: '#b91c1c',
      PRIMARY_800: '#991b1b',
      PRIMARY_900: '#7f1d1d',

      // Secondary Colors
      SECONDARY: '#1e40af', // Medical blue
      SECONDARY_LIGHT: '#3b82f6',
      SECONDARY_DARK: '#1e3a8a',
      SECONDARY_50: '#eff6ff',
      SECONDARY_100: '#dbeafe',
      SECONDARY_200: '#bfdbfe',
      SECONDARY_500: '#3b82f6',
      SECONDARY_600: '#2563eb',
      SECONDARY_700: '#1d4ed8',
      SECONDARY_800: '#1e40af',
      SECONDARY_900: '#1e3a8a',

      // Accent Colors
      ACCENT: '#059669', // Medical green
      ACCENT_LIGHT: '#10b981',
      ACCENT_DARK: '#047857',

      // Neutral Colors
      WHITE: '#ffffff',
      GRAY_50: '#f9fafb',
      GRAY_100: '#f3f4f6',
      GRAY_200: '#e5e7eb',
      GRAY_300: '#d1d5db',
      GRAY_400: '#9ca3af',
      GRAY_500: '#6b7280',
      GRAY_600: '#4b5563',
      GRAY_700: '#374151',
      GRAY_800: '#1f2937',
      GRAY_900: '#111827',

      // Status Colors
      SUCCESS: '#059669',
      WARNING: '#d97706',
      ERROR: '#dc2626',
      INFO: '#2563eb',

      // Background Colors
      BACKGROUND: '#ffffff',
      BACKGROUND_SECONDARY: '#f9fafb',
      SURFACE: '#ffffff',
      SURFACE_SECONDARY: '#f3f4f6',
    },

    // Typography
    TYPOGRAPHY: {
      FONT_FAMILY: {
        PRIMARY: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
        SECONDARY: "'Poppins', sans-serif",
        MONO: "'JetBrains Mono', 'Fira Code', monospace",
      },
      FONT_SIZE: {
        XS: '0.75rem',    // 12px
        SM: '0.875rem',   // 14px
        BASE: '1rem',     // 16px
        LG: '1.125rem',   // 18px
        XL: '1.25rem',    // 20px
        '2XL': '1.5rem',  // 24px
        '3XL': '1.875rem', // 30px
        '4XL': '2.25rem', // 36px
        '5XL': '3rem',    // 48px
        '6XL': '3.75rem', // 60px
      },
      FONT_WEIGHT: {
        LIGHT: '300',
        NORMAL: '400',
        MEDIUM: '500',
        SEMIBOLD: '600',
        BOLD: '700',
        EXTRABOLD: '800',
      },
      LINE_HEIGHT: {
        TIGHT: '1.25',
        NORMAL: '1.5',
        RELAXED: '1.75',
      },
    },

    // Spacing
    SPACING: {
      XS: '0.25rem',   // 4px
      SM: '0.5rem',    // 8px
      MD: '1rem',      // 16px
      LG: '1.5rem',    // 24px
      XL: '2rem',      // 32px
      '2XL': '3rem',   // 48px
      '3XL': '4rem',   // 64px
      '4XL': '6rem',   // 96px
    },

    // Border Radius
    BORDER_RADIUS: {
      NONE: '0',
      SM: '0.25rem',   // 4px
      MD: '0.375rem',  // 6px
      LG: '0.5rem',    // 8px
      XL: '0.75rem',   // 12px
      '2XL': '1rem',   // 16px
      FULL: '9999px',
    },

    // Shadows
    SHADOWS: {
      SM: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      MD: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      LG: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      XL: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      INNER: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    },

    // Transitions
    TRANSITIONS: {
      FAST: '150ms ease-in-out',
      NORMAL: '300ms ease-in-out',
      SLOW: '500ms ease-in-out',
    },

    // Breakpoints
    BREAKPOINTS: {
      SM: '576px',
      MD: '768px',
      LG: '992px',
      XL: '1200px',
      XXL: '1400px',
    },
  } as const,
} as const;
