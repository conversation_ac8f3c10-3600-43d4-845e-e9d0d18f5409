/* Modern Admin Tables Styles */
.modern-admin-tables {
  background: var(--color-background);
  min-height: 100vh;
}

/* Tab Navigation */
.table-tabs-container {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.modern-tabs {
  border-bottom: none;
  padding: 0 2rem;
}

.modern-tabs .nav-link {
  border: none;
  border-radius: 0;
  padding: 1.5rem 2rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-600);
  transition: var(--transition-normal);
  position: relative;
  display: flex;
  align-items: center;
  background: transparent;
}

.modern-tabs .nav-link:hover {
  color: var(--color-primary);
  background: var(--color-primary-50);
}

.modern-tabs .nav-link.active {
  color: var(--color-primary);
  background: transparent;
  border-bottom: 3px solid var(--color-primary);
}

.modern-tabs .nav-link .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Tab Content */
.modern-tab-content {
  padding: 2rem;
}

/* Sub-tabs */
.sub-tabs-container {
  margin-bottom: 2rem;
}

.sub-tabs {
  background: var(--color-gray-50);
  border-radius: var(--border-radius-lg);
  padding: 0.5rem;
}

.sub-tabs .nav-link {
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  margin: 0 0.25rem;
}

.sub-tabs .nav-link:hover {
  color: var(--color-primary);
  background: var(--color-white);
}

.sub-tabs .nav-link.active {
  color: var(--color-white);
  background: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.sub-tabs .nav-link .badge {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
.sub-tab-content {
  margin-top: 1.5rem;
}

/* Section Headers */
.section-header-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-gray-200);
}

.section-info .section-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.section-info .section-subtitle {
  color: var(--color-gray-600);
  margin-bottom: 0;
}

/* Table Headers */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem 0;
}

.table-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Search Box */
.search-box {
  position: relative;
  width: 300px;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
  z-index: 2;
}

.search-box .form-control {
  padding-left: 2.5rem;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-normal);
}

.search-box .form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Modern Table Container */
.modern-table-container {
  background: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
}

.modern-table {
  margin-bottom: 0;
  font-size: var(--font-size-sm);
}

.modern-table thead {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.modern-table thead th {
  color: var(--color-white);
  font-weight: var(--font-weight-semibold);
  padding: 1.25rem 1rem;
  border: none;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modern-table tbody tr {
  transition: var(--transition-fast);
  border-bottom: 1px solid var(--color-gray-100);
}

.modern-table tbody tr:hover {
  background-color: var(--color-gray-50);
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table tbody td {
  padding: 1rem;
  vertical-align: middle;
  border: none;
}

/* Table Cell Styles */
.row-number {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-500);
  width: 60px;
}

.id-badge {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-mono);
}

.blood-type-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
}

.date-info {
  display: flex;
  flex-direction: column;
}

.date {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.time-ago {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.location-info,
.hospital-info,
.contact-info {
  display: flex;
  align-items: center;
}

.remarks-text,
.message-text {
  color: var(--color-gray-700);
  line-height: var(--line-height-normal);
}

.amount-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.amount-value {
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  font-size: var(--font-size-base);
}

.amount-unit {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  text-transform: uppercase;
}

/* Status and Urgency Badges */
.status-badge,
.urgency-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.status-approved {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.status-cancelled {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-buttons .btn {
  padding: 0.5rem;
  border-radius: var(--border-radius-md);
  transition: var(--transition-normal);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Feedback Specific Styles */
.feedback-content {
  max-width: 300px;
}

.feedback-message {
  margin-bottom: 0;
  line-height: var(--line-height-normal);
}

.rating-stars {
  display: flex;
  align-items: center;
}

.rating-value {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

/* No Data Message */
.no-data-row td {
  padding: 3rem 1rem !important;
}

.no-data-message {
  text-align: center;
}

.no-data-message i {
  opacity: 0.5;
}

.no-data-message h5 {
  margin-bottom: 0.5rem;
  font-weight: var(--font-weight-semibold);
}

.no-data-message p {
  margin-bottom: 0;
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 1199.98px) {
  .modern-tabs {
    padding: 0 1rem;
  }

  .modern-tabs .nav-link {
    padding: 1.25rem 1.5rem;
  }

  .modern-tab-content {
    padding: 1.5rem;
  }

  .search-box {
    width: 250px;
  }
}

@media (max-width: 991.98px) {
  .section-header-with-action {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .search-box {
    width: 100%;
    max-width: 400px;
  }

  .modern-tabs .nav-link {
    padding: 1rem;
    font-size: var(--font-size-sm);
  }

  .sub-tabs .nav-link {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 767.98px) {
  .modern-tabs {
    padding: 0 0.5rem;
    flex-wrap: wrap;
  }

  .modern-tabs .nav-link {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.25rem;
  }

  .modern-tab-content {
    padding: 1rem;
  }

  .sub-tabs {
    flex-direction: column;
  }

  .sub-tabs .nav-link {
    margin: 0.125rem 0;
    justify-content: center;
  }

  .table-title {
    font-size: var(--font-size-lg);
  }

  .section-info .section-title {
    font-size: var(--font-size-xl);
  }

  /* Make tables horizontally scrollable on mobile */
  .modern-table-container {
    overflow-x: auto;
  }

  .modern-table {
    min-width: 800px;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.75rem 0.5rem;
    font-size: var(--font-size-xs);
  }

  .action-buttons .btn {
    width: 2rem;
    height: 2rem;
    padding: 0.25rem;
  }

  .blood-type-badge,
  .status-badge,
  .urgency-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
  }
}

@media (max-width: 575.98px) {
  .modern-tabs .nav-link {
    padding: 0.5rem 0.75rem;
  }

  .modern-tab-content {
    padding: 0.75rem;
  }

  .table-header {
    padding: 0.5rem 0;
  }

  .table-title {
    font-size: var(--font-size-base);
  }

  .section-info .section-title {
    font-size: var(--font-size-lg);
  }

  .modern-table {
    min-width: 700px;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.5rem 0.25rem;
  }

  .id-badge {
    padding: 0.125rem 0.25rem;
    font-size: 0.625rem;
  }

  .action-buttons {
    gap: 0.25rem;
  }

  .action-buttons .btn {
    width: 1.75rem;
    height: 1.75rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .modern-tabs .nav-link,
  .sub-tabs .nav-link,
  .modern-table tbody tr,
  .action-buttons .btn {
    transition: none;
  }

  .action-buttons .btn:hover {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .modern-table-container {
    border: 2px solid var(--color-gray-800);
  }

  .modern-tabs .nav-link.active {
    border-bottom: 4px solid var(--color-primary);
  }

  .blood-type-badge,
  .status-badge,
  .urgency-badge {
    border: 1px solid var(--color-gray-800);
  }
}

/* Print Styles */
@media print {
  .modern-tabs,
  .sub-tabs-container,
  .table-actions,
  .action-buttons {
    display: none;
  }

  .modern-tab-content {
    padding: 0;
  }

  .modern-table-container {
    box-shadow: none;
    border: 1px solid var(--color-gray-400);
  }

  .modern-table thead {
    background: none !important;
    color: var(--color-gray-900) !important;
  }

  .modern-table thead th {
    color: var(--color-gray-900) !important;
    border-bottom: 2px solid var(--color-gray-400);
  }

  .modern-table tbody tr {
    break-inside: avoid;
  }
}
  