import { Component, OnInit, AfterViewInit } from '@angular/core';
import { PendingListComponent } from './pending-list/pending-list.component';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import Swal from 'sweetalert2';
import { QtyComponent } from './qty/qty.component';
import { DESIGN_TOKENS, CSS_CLASSES } from '../../constants';
import Aos from 'aos';

@Component({
  selector: 'app-admin',
  standalone: true,
  imports: [PendingListComponent, FormsModule, CommonModule, QtyComponent],
  templateUrl: './admin.component.html',
  styleUrl: './admin.component.css'
})
export class AdminComponent implements OnInit, AfterViewInit {
  public id: string | null = null;
  public appointmentList: any;
  public admin: any;
  public inventoryList: any = [];
  public recentActivities: any = {
    donations: [],
    requests: []
  };
  public systemAlerts: any[] = [];

  // Design System
  readonly DESIGN = DESIGN_TOKENS;
  readonly CSS = CSS_CLASSES;

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.id = params['id'];
      console.log("ID from query params:", this.id);
      this.loadDeatails(this.id);
      this.loadAppointments();
      this.loadInventory();
      this.loadRecentActivities();
      this.loadSystemAlerts();
    });
  }

  ngAfterViewInit(): void {
    // Initialize AOS animations
    Aos.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });
  }

  loadAppointments(){
    this.http.get("http://localhost:8080/Appointment/pendings").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.appointmentList = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }

  loadDeatails(id:any){
    this.http.get(`http://localhost:8080/Admin/findById/${id}`).subscribe(
      data => {
        console.log("Data loaded:", data);
        this.admin = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }


  deleteAdmin(adminID:any){
    console.log(adminID);
  }


  public temp:any = {};
  updateAdminDetails(admin:any){
    this.temp=admin;

  }
  saveAdmin(){
    this.http.put("http://localhost:8080/Admin/update",this.temp, { responseType: 'text' }).subscribe(data=>{
      Swal.fire({
        title: "Updated the Admin",
        icon: "success"
      });
    })
  }


  loadInventory(){
    this.http.get("http://localhost:8080/Inventory/all").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.inventoryList = data;
        this.makeTworows(this.inventoryList);
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }

  public inventoryList01: any = [];
  public inventoryList02: any = [];

  makeTworows(inventoryList: any) {
    for (let i = 0; i < 4 && i < inventoryList.length; i++) {
      this.inventoryList01.push(inventoryList[i]);
    }

    for (let i = 4; i < 8 && i < inventoryList.length; i++) {
      this.inventoryList02.push(inventoryList[i]);
    }
  }

  // New methods for enhanced dashboard
  getTotalUnits(): number {
    return this.inventoryList.reduce((total: number, item: any) => total + (item.amount || 0), 0);
  }

  getActiveDonors(): number {
    // This would typically come from an API call
    return 1250; // Placeholder
  }

  getPartnerHospitals(): number {
    // This would typically come from an API call
    return 45; // Placeholder
  }

  getPendingRequests(): number {
    return this.appointmentList?.length || 0;
  }

  onManageStock(inventory: any): void {
    console.log('Managing stock for:', inventory);
    // Implement stock management logic
    Swal.fire({
      title: `Manage ${inventory.bloodType} Stock`,
      text: `Current amount: ${inventory.amount} units`,
      icon: 'info',
      showCancelButton: true,
      confirmButtonText: 'Update Stock',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        // Navigate to stock management page or open modal
        this.router.navigate(['/manage-stock'], { queryParams: { type: inventory.bloodType } });
      }
    });
  }

  loadRecentActivities(): void {
    // This would typically be an API call
    this.recentActivities = {
      donations: [
        {
          donorName: 'John Doe',
          bloodType: 'O+',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
        },
        {
          donorName: 'Jane Smith',
          bloodType: 'A-',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
        }
      ]
    };
  }

  loadSystemAlerts(): void {
    // This would typically be an API call
    this.systemAlerts = [
      {
        type: 'warning',
        message: 'O- blood type is running low (8 units remaining)',
        timestamp: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
      },
      {
        type: 'info',
        message: 'New hospital partnership request received',
        timestamp: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
      }
    ];
  }

  getAlertIconClass(type: string): string {
    const iconClasses: { [key: string]: string } = {
      'warning': 'bg-warning',
      'error': 'bg-danger',
      'info': 'bg-info',
      'success': 'bg-success'
    };
    return iconClasses[type] || 'bg-secondary';
  }

  getAlertIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'warning': 'bi-exclamation-triangle-fill',
      'error': 'bi-x-circle-fill',
      'info': 'bi-info-circle-fill',
      'success': 'bi-check-circle-fill'
    };
    return icons[type] || 'bi-info-circle-fill';
  }

  formatTime(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes} minutes ago`;
    } else if (hours < 24) {
      return `${hours} hours ago`;
    } else {
      return `${days} days ago`;
    }
  }
}
