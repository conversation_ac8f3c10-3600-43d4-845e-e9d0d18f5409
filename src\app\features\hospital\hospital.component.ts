import { Component, OnInit } from '@angular/core';
import { RequestComponent } from './request/request.component';
import { BloodRequestComponent } from './blood-request/blood-request.component';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule, NgFor } from '@angular/common';
import { QtyComponent } from '../admin/qty/qty.component';
import { FormsModule } from '@angular/forms';
import { HospitalService } from '../../core/services/hospital.service';
import { InventoryService } from '../../core/services/inventory.service';
import { Hospital, BloodRequest } from '../../models';
import { BloodInventory } from '../../models/inventory.model';
import Aos from 'aos';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-hospital',
  standalone: true,
  imports: [RequestComponent,BloodRequestComponent,NgFor,QtyComponent,FormsModule,CommonModule],
  templateUrl: './hospital.component.html',
  styleUrl: './hospital.component.css'
})
export class HospitalComponent implements OnInit{
  public id: string | null = null;
  public hospital: Hospital = {} as Hospital;
  public bloodRequestLists: BloodRequest[] = [];
  public inventoryList: BloodInventory[] = [];
  public inventoryList01: BloodInventory[] = [];
  public inventoryList02: BloodInventory[] = [];
  public RequestListsOfApproved: any[] = [];
  public RequestListsOfCompleted: BloodRequest[] = [];

  constructor(
    private route: ActivatedRoute,
    private hospitalService: HospitalService,
    private inventoryService: InventoryService,
    private router: Router
  ) {}

  ngOnInit() {
    Aos.init();
    this.route.queryParams.subscribe(params => {
      this.id = params['id'];
      console.log("ID from query params:", this.id);
    });
    this.findHospitalDetails();
    this.loadBloodRequestList();
    this.loadInventory();
    this.loadhospitalCompletedApproved();
    this.loadCompletedRequests();
  }


  findHospitalDetails(){
    if (this.id) {
      this.hospitalService.getHospitalById(this.id).subscribe({
        next: (data: Hospital) => {
          console.log("Data loaded of hospital:", data);
          this.hospital = data;
        },
        error: (error: any) => {
          console.error("Error loading hospital details:", error);
        }
      });
    }
  }

  loadCompletedRequests(){
    this.hospitalService.getCompletedBloodRequests().subscribe({
      next: (data: BloodRequest[]) => {
        console.log("Data loaded:", data);
        this.RequestListsOfCompleted = data;
      },
      error: (error: any) => {
        console.error("Error loading completed requests:", error);
      }
    });
  }

  loadBloodRequestList(){
    this.hospitalService.getPendingBloodRequests().subscribe(
      (data: BloodRequest[]) => {
        console.log("Data loaded:", data);
        this.bloodRequestLists = data;
      },
      (error: any) => {
        console.error("Error loading pending requests:", error);
      }
    );
  }

  delete(requests:any){
    console.log(requests);
  }

  loadInventory(){
    this.inventoryService.getAllInventory().subscribe(
      (data: BloodInventory[]) => {
        console.log("Data loaded:", data);
        this.inventoryList = data;
        this.makeTworows(this.inventoryList);
      },
      (error: any) => {
        console.error("Error loading inventory:", error);
      }
    );
  }

  makeTworows(inventoryList:any){
    for (let i = 0; i < 4 && i < inventoryList.length; i++) {
      this.inventoryList01.push(inventoryList[i]);
    }
    
    for (let i = 4; i < 8 && i < inventoryList.length; i++) {
      this.inventoryList02.push(inventoryList[i]);
    }
  }


  loadhospitalCompletedApproved(){
    this.hospitalService.getCompletedApproveRequests().subscribe(
      (data: any[]) => {
        console.log("Data loaded:", data);
        this.RequestListsOfApproved = data;
      },
      (error: any) => {
        console.error("Error loading approved requests:", error);
      }
    );
  }

  public hospitalTemp:any = {}
  updateHospitalDetails(hospital:any){
    this.hospitalTemp = hospital;

  }
  


  deleteHospital(){

  }

  saveHospital(){
    this.hospitalService.updateHospital(this.hospitalTemp).subscribe(
      (data: string) => {
        Swal.fire({
          title: "Updated the Hospital",
          icon: "success"
        });
      },
      (error: any) => {
        console.error("Error updating hospital:", error);
        Swal.fire({
          title: "Error updating hospital",
          text: "Please try again later",
          icon: "error"
        });
      }
    );
  }

}
