import { NgIf, NgClass } from '@angular/common';
import { Component, OnInit, HostListener, AfterViewInit, ElementRef } from '@angular/core';
import { Router, RouterLink, RouterLinkActive, NavigationEnd } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import { APP_CONSTANTS, ASSETS, DESIGN_TOKENS, CSS_CLASSES } from '../../constants';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, NgIf, NgClass],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent implements OnInit, AfterViewInit {
  // UI State
  showSignUpButton = true;
  showDonationButton = true;
  showBreadcrumb = false;
  isScrolled = false;
  isSignedIn = false;
  signButtonText = 'Sign In'; // Default button text
  currentPageTitle = '';

  // Design System
  readonly DESIGN = DESIGN_TOKENS;
  readonly CSS = CSS_CLASSES;

  // Constants
  readonly ASSETS = ASSETS;
  readonly APP_CONSTANTS = APP_CONSTANTS;

  // Protected routes that should show breadcrumbs
  private readonly PROTECTED_ROUTES = [
    APP_CONSTANTS.ROUTES.ADMIN,
    APP_CONSTANTS.ROUTES.HOSPITAL,
    APP_CONSTANTS.ROUTES.DONOR_HOME,
    APP_CONSTANTS.ROUTES.ADD_HOSPITAL
  ];

  // Page title mapping
  private readonly PAGE_TITLES: { [key: string]: string } = {
    [APP_CONSTANTS.ROUTES.ADMIN]: 'Admin Dashboard',
    [APP_CONSTANTS.ROUTES.HOSPITAL]: 'Hospital Dashboard',
    [APP_CONSTANTS.ROUTES.DONOR_HOME]: 'Donor Dashboard',
    [APP_CONSTANTS.ROUTES.ADD_HOSPITAL]: 'Add Hospital',
    [APP_CONSTANTS.ROUTES.ABOUT]: 'About Us'
  };

  constructor(
    private router: Router,
    private authService: AuthService,
    private elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    // Subscribe to the logged-in status
    this.authService.isLoggedIn().subscribe(isLoggedIn => {
      this.isSignedIn = isLoggedIn;
      this.signButtonText = this.isSignedIn ? 'Sign Out' : 'Sign In';
    });

    // Subscribe to router events to manage UI state
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // Handle sign up button visibility
        this.showSignUpButton = event.url !== `/${APP_CONSTANTS.ROUTES.LOGIN}`;

        // Handle donation button visibility
        const hideButtonPages = [
          `/${APP_CONSTANTS.ROUTES.ADMIN}`,
          `/${APP_CONSTANTS.ROUTES.HOSPITAL}`,
          `/${APP_CONSTANTS.ROUTES.DONOR_HOME}`
        ];
        this.showDonationButton = !hideButtonPages.some(page => event.url.includes(page));

        // Handle breadcrumb visibility
        const currentRoute = event.url.split('/')[1];
        this.showBreadcrumb = this.PROTECTED_ROUTES.includes(currentRoute as any);

        // Set current page title for breadcrumb
        this.currentPageTitle = this.PAGE_TITLES[currentRoute] || 'Dashboard';
      }
    });
  }

  ngAfterViewInit(): void {
    // Initialize any third-party components or behaviors
    this.initializeDropdowns();
  }

  @HostListener('window:scroll', [])
  onWindowScroll(): void {
    // Add scrolled class to navbar when page is scrolled
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.isScrolled = scrollPosition > 50;

    // Apply scrolled class to navbar element
    const navbar = this.elementRef.nativeElement.querySelector('.navbar');
    if (navbar) {
      if (this.isScrolled) {
        navbar.classList.add('navbar-scrolled');
      } else {
        navbar.classList.remove('navbar-scrolled');
      }
    }
  }

  toggleSignIn(): void {
    if (this.isSignedIn) {
      this.authService.logout(); // Call logout if currently signed in
    } else {
      this.router.navigate([APP_CONSTANTS.ROUTES.LOGIN]); // Navigate to login page if not signed in
    }
  }

  private initializeDropdowns(): void {
    // Initialize Bootstrap dropdowns if needed
    // This is a placeholder for any initialization code that might be needed
  }
}