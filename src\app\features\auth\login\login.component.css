/* Modern Login Component Styles */

.modern-login-section {
  min-height: 100vh;
  background: var(--color-background);
  position: relative;
}

/* Hero Section */
.login-hero {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 50%, var(--color-secondary-dark) 100%);
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-overlay {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 2rem;
}

.hero-content {
  max-width: 500px;
  margin: 0 auto;
}

.hero-logo {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.hero-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: 2rem;
}

.hero-stats {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  color: var(--color-white);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Login Form Container */
.login-form-container {
  max-width: 480px;
  width: 100%;
  padding: 2rem;
}

.login-header {
  margin-bottom: 2rem;
}

.brand-logo {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.login-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.login-subtitle {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
}

/* Modern Form Styles */
.modern-form {
  width: 100%;
}

.form-group {
  position: relative;
}

.form-label {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
}

.input-wrapper {
  position: relative;
}

.form-control {
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  background-color: var(--color-white);
  width: 100%;
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  outline: none;
}

.form-control.is-valid {
  border-color: var(--color-success);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
}

.form-control.is-invalid {
  border-color: var(--color-error);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc2626'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.5 5.5 1 1m0-1-1 1'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 2;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
  z-index: 2;
}

.password-toggle:hover {
  color: var(--color-gray-600);
  background-color: var(--color-gray-100);
}

.password-toggle:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.5rem;
  font-size: var(--font-size-sm);
  color: var(--color-error);
  font-weight: var(--font-weight-medium);
}

/* Form Options */
.form-options {
  margin: 1.5rem 0;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-check-input {
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid var(--color-gray-300);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.form-check-input:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.form-check-input:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-check-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  cursor: pointer;
}

.forgot-password-link {
  color: var(--color-primary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.forgot-password-link:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Submit Button */
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  border: none;
  padding: 1rem 2rem;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary-800) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  background: var(--color-gray-300);
  color: var(--color-gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.125rem;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--color-gray-200);
}

.divider span {
  background: var(--color-white);
  padding: 0 1rem;
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  position: relative;
  z-index: 1;
}

/* Social Login */
.social-login .btn {
  border: 2px solid var(--color-gray-200);
  color: var(--color-gray-700);
  background: var(--color-white);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-normal);
}

.social-login .btn:hover {
  border-color: var(--color-gray-300);
  background: var(--color-gray-50);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Sign Up Link */
.signup-link a {
  transition: var(--transition-fast);
}

.signup-link a.hover-underline:hover {
  text-decoration: underline !important;
}

/* Footer Links */
.login-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-gray-200);
}

.footer-links a {
  font-size: var(--font-size-sm);
  transition: var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-primary) !important;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .login-hero {
    display: none;
  }

  .modern-login-section {
    padding: 2rem 0;
  }

  .login-form-container {
    max-width: 600px;
    padding: 2rem 1rem;
  }
}

@media (max-width: 575.98px) {
  .modern-login-section {
    padding: 1rem 0;
  }

  .login-form-container {
    padding: 1rem;
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .login-title {
    font-size: var(--font-size-2xl);
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    padding: 0.5rem;
  }

  .form-control {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
  }

  .input-icon {
    left: 0.875rem;
  }

  .password-toggle {
    right: 0.875rem;
  }

  .btn-primary {
    padding: 0.875rem 1.5rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hero-logo,
  .brand-logo,
  .btn-primary,
  .form-control,
  .password-toggle {
    animation: none;
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .form-control {
    border-width: 3px;
  }

  .form-control:focus {
    border-width: 3px;
    box-shadow: 0 0 0 3px var(--color-primary);
  }

  .btn-primary {
    border: 2px solid var(--color-primary-dark);
  }
}

/* Focus Management */
.form-control:focus,
.form-check-input:focus,
.btn:focus,
.password-toggle:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Loading State */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Error States */
.form-control.is-invalid:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Success States */
.form-control.is-valid:focus {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}