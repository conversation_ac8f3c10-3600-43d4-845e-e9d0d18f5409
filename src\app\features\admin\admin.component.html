<div class="modern-admin-dashboard">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-8">
          <div class="header-content">
            <h1 class="dashboard-title text-gradient-primary">
              <i class="bi bi-speedometer2 me-3"></i>
              Admin Dashboard
            </h1>
            <p class="dashboard-subtitle">
              Manage blood inventory, monitor donations, and oversee hospital requests
            </p>
          </div>
        </div>
        <div class="col-md-4 text-end">
          <div class="header-actions">
            <button class="btn btn-primary hover-lift me-2">
              <i class="bi bi-plus-circle me-2"></i>
              Add Hospital
            </button>
            <button class="btn btn-outline-primary hover-lift">
              <i class="bi bi-download me-2"></i>
              Export Data
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Blood Inventory Section -->
  <div class="dashboard-section">
    <div class="container-fluid">
      <div class="section-header">
        <h2 class="section-title">
          <i class="bi bi-droplet-fill text-primary me-2"></i>
          Blood Inventory Overview
        </h2>
        <p class="section-subtitle">Current blood stock levels across all types</p>
      </div>

      <!-- Blood Type Cards Grid -->
      <div class="blood-inventory-grid">
        <div class="row g-4">
          <div class="col-xl-3 col-lg-4 col-md-6" *ngFor="let inventory of inventoryList; let i = index">
            <app-qty
              [inventory]="inventory"
              (manageStock)="onManageStock($event)"
              [attr.data-aos]="'fade-up'"
              [attr.data-aos-delay]="i * 100">
            </app-qty>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="quick-stats mt-5">
        <div class="row g-4">
          <div class="col-lg-3 col-md-6">
            <div class="stat-card hover-lift">
              <div class="stat-icon bg-primary">
                <i class="bi bi-droplet-fill"></i>
              </div>
              <div class="stat-content">
                <h3 class="stat-number">{{ getTotalUnits() }}</h3>
                <p class="stat-label">Total Units</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="stat-card hover-lift">
              <div class="stat-icon bg-success">
                <i class="bi bi-heart-pulse"></i>
              </div>
              <div class="stat-content">
                <h3 class="stat-number">{{ getActiveDonors() }}</h3>
                <p class="stat-label">Active Donors</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="stat-card hover-lift">
              <div class="stat-icon bg-info">
                <i class="bi bi-building"></i>
              </div>
              <div class="stat-content">
                <h3 class="stat-number">{{ getPartnerHospitals() }}</h3>
                <p class="stat-label">Partner Hospitals</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="stat-card hover-lift">
              <div class="stat-icon bg-warning">
                <i class="bi bi-clock-history"></i>
              </div>
              <div class="stat-content">
                <h3 class="stat-number">{{ getPendingRequests() }}</h3>
                <p class="stat-label">Pending Requests</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Admin Profile Section -->
  <div class="dashboard-section bg-gray-50">
    <div class="container-fluid">
      <div class="section-header">
        <h2 class="section-title">
          <i class="bi bi-person-badge text-primary me-2"></i>
          Administrator Profile
        </h2>
        <p class="section-subtitle">Manage your account information and settings</p>
      </div>

      <div class="row justify-content-center">
        <div class="col-lg-8">
          <div class="admin-profile-card" data-aos="fade-up">
            <div class="profile-header">
              <div class="profile-avatar">
                <img src="https://i.imgur.com/bDLhJiP.jpg" alt="Admin Avatar" class="avatar-image">
                <div class="avatar-badge">
                  <i class="bi bi-shield-check"></i>
                </div>
              </div>
              <div class="profile-info">
                <div class="admin-id-badge">
                  <i class="bi bi-person-badge me-2"></i>
                  Admin ID: {{ admin?.adminID }}
                </div>
                <h3 class="profile-name">{{ admin?.name }}</h3>
                <div class="profile-details">
                  <div class="detail-item">
                    <i class="bi bi-envelope me-2"></i>
                    <span>{{ admin?.email }}</span>
                  </div>
                  <div class="detail-item">
                    <i class="bi bi-telephone me-2"></i>
                    <span>{{ admin?.contactNumber }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="profile-actions">
              <button
                type="button"
                class="btn btn-primary hover-lift me-3"
                data-bs-toggle="modal"
                data-bs-target="#editAdminModal"
                (click)="updateAdminDetails(admin)">
                <i class="bi bi-pencil-square me-2"></i>
                Edit Profile
              </button>
              <button
                class="btn btn-outline-danger hover-lift"
                (click)="deleteAdmin(admin?.adminID)">
                <i class="bi bi-trash3 me-2"></i>
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Admin Modal -->
  <div class="modal fade" id="editAdminModal" tabindex="-1" aria-labelledby="editAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="editAdminModalLabel">
            <i class="bi bi-pencil-square me-2"></i>
            Update Administrator Details
          </h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form class="modern-form">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="adminID" class="form-label">
                  <i class="bi bi-person-badge me-2"></i>
                  Admin ID
                </label>
                <input
                  disabled
                  type="text"
                  class="form-control"
                  id="adminID"
                  name="adminID"
                  [(ngModel)]="temp.adminID" />
              </div>
              <div class="col-md-6 mb-3">
                <label for="adminName" class="form-label">
                  <i class="bi bi-person me-2"></i>
                  Full Name
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="adminName"
                  name="name"
                  required
                  [(ngModel)]="temp.name"
                  placeholder="Enter full name" />
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="adminEmail" class="form-label">
                  <i class="bi bi-envelope me-2"></i>
                  Email Address
                </label>
                <input
                  type="email"
                  class="form-control"
                  id="adminEmail"
                  name="email"
                  required
                  [(ngModel)]="temp.email"
                  placeholder="Enter email address" />
              </div>
              <div class="col-md-6 mb-3">
                <label for="adminContact" class="form-label">
                  <i class="bi bi-telephone me-2"></i>
                  Contact Number
                </label>
                <input
                  type="tel"
                  class="form-control"
                  id="adminContact"
                  name="contactNumber"
                  required
                  [(ngModel)]="temp.contactNumber"
                  placeholder="Enter contact number" />
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-2"></i>
            Cancel
          </button>
          <button
            type="button"
            class="btn btn-primary"
            (click)="saveAdmin()"
            data-bs-dismiss="modal">
            <i class="bi bi-check-circle me-2"></i>
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Appointment Management Section -->
  <div class="dashboard-section">
    <div class="container-fluid">
      <div class="section-header">
        <h2 class="section-title">
          <i class="bi bi-calendar-check text-primary me-2"></i>
          Appointment Management
        </h2>
        <p class="section-subtitle">Review and manage donation appointments and blood requests</p>
      </div>

      <div class="appointment-manager-container">
        <app-pending-list [appointments]="appointmentList"></app-pending-list>
      </div>
    </div>
  </div>

  <!-- Recent Activity Section -->
  <div class="dashboard-section bg-gray-50">
    <div class="container-fluid">
      <div class="section-header">
        <h2 class="section-title">
          <i class="bi bi-activity text-primary me-2"></i>
          Recent Activity
        </h2>
        <p class="section-subtitle">Latest system activities and notifications</p>
      </div>

      <div class="row g-4">
        <div class="col-lg-6">
          <div class="activity-card">
            <div class="activity-header">
              <h4 class="activity-title">
                <i class="bi bi-clock-history me-2"></i>
                Recent Donations
              </h4>
            </div>
            <div class="activity-list">
              <div class="activity-item" *ngFor="let activity of recentActivities?.donations">
                <div class="activity-icon bg-success">
                  <i class="bi bi-droplet-fill"></i>
                </div>
                <div class="activity-content">
                  <p class="activity-text">{{ activity.donorName }} donated {{ activity.bloodType }}</p>
                  <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-6">
          <div class="activity-card">
            <div class="activity-header">
              <h4 class="activity-title">
                <i class="bi bi-exclamation-triangle me-2"></i>
                System Alerts
              </h4>
            </div>
            <div class="activity-list">
              <div class="activity-item" *ngFor="let alert of systemAlerts">
                <div class="activity-icon" [ngClass]="getAlertIconClass(alert.type)">
                  <i class="bi" [ngClass]="getAlertIcon(alert.type)"></i>
                </div>
                <div class="activity-content">
                  <p class="activity-text">{{ alert.message }}</p>
                  <span class="activity-time">{{ formatTime(alert.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>