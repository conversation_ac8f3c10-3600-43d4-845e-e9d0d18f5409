/* Blood Bank Management System - Global Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap');

/* CSS Custom Properties - Design System */
:root {
  /* Colors - Primary */
  --color-primary: #dc2626;
  --color-primary-light: #ef4444;
  --color-primary-dark: #b91c1c;
  --color-primary-50: #fef2f2;
  --color-primary-100: #fee2e2;
  --color-primary-200: #fecaca;
  --color-primary-500: #ef4444;
  --color-primary-600: #dc2626;
  --color-primary-700: #b91c1c;
  --color-primary-800: #991b1b;
  --color-primary-900: #7f1d1d;

  /* Colors - Secondary */
  --color-secondary: #1e40af;
  --color-secondary-light: #3b82f6;
  --color-secondary-dark: #1e3a8a;
  --color-secondary-50: #eff6ff;
  --color-secondary-100: #dbeafe;
  --color-secondary-200: #bfdbfe;
  --color-secondary-500: #3b82f6;
  --color-secondary-600: #2563eb;
  --color-secondary-700: #1d4ed8;
  --color-secondary-800: #1e40af;
  --color-secondary-900: #1e3a8a;

  /* Colors - Accent */
  --color-accent: #059669;
  --color-accent-light: #10b981;
  --color-accent-dark: #047857;

  /* Colors - Neutral */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Colors - Status */
  --color-success: #059669;
  --color-warning: #d97706;
  --color-error: #dc2626;
  --color-info: #2563eb;

  /* Colors - Background */
  --color-background: #ffffff;
  --color-background-secondary: #f9fafb;
  --color-surface: #ffffff;
  --color-surface-secondary: #f3f4f6;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'Poppins', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;

  /* Border Radius */
  --border-radius-none: 0;
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Utilities */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-accent { color: var(--color-accent) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }
.text-info { color: var(--color-info) !important; }

.text-gray-50 { color: var(--color-gray-50) !important; }
.text-gray-100 { color: var(--color-gray-100) !important; }
.text-gray-200 { color: var(--color-gray-200) !important; }
.text-gray-300 { color: var(--color-gray-300) !important; }
.text-gray-400 { color: var(--color-gray-400) !important; }
.text-gray-500 { color: var(--color-gray-500) !important; }
.text-gray-600 { color: var(--color-gray-600) !important; }
.text-gray-700 { color: var(--color-gray-700) !important; }
.text-gray-800 { color: var(--color-gray-800) !important; }
.text-gray-900 { color: var(--color-gray-900) !important; }

.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }
.font-extrabold { font-weight: var(--font-weight-extrabold) !important; }

.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }
.text-5xl { font-size: var(--font-size-5xl) !important; }
.text-6xl { font-size: var(--font-size-6xl) !important; }

/* Background Utilities */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-primary-light { background-color: var(--color-primary-light) !important; }
.bg-primary-dark { background-color: var(--color-primary-dark) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-secondary-light { background-color: var(--color-secondary-light) !important; }
.bg-secondary-dark { background-color: var(--color-secondary-dark) !important; }
.bg-accent { background-color: var(--color-accent) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-error { background-color: var(--color-error) !important; }
.bg-info { background-color: var(--color-info) !important; }

.bg-gray-50 { background-color: var(--color-gray-50) !important; }
.bg-gray-100 { background-color: var(--color-gray-100) !important; }
.bg-gray-200 { background-color: var(--color-gray-200) !important; }
.bg-gray-300 { background-color: var(--color-gray-300) !important; }
.bg-gray-400 { background-color: var(--color-gray-400) !important; }
.bg-gray-500 { background-color: var(--color-gray-500) !important; }
.bg-gray-600 { background-color: var(--color-gray-600) !important; }
.bg-gray-700 { background-color: var(--color-gray-700) !important; }
.bg-gray-800 { background-color: var(--color-gray-800) !important; }
.bg-gray-900 { background-color: var(--color-gray-900) !important; }

/* Border Radius Utilities */
.rounded-none { border-radius: var(--border-radius-none) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-2xl { border-radius: var(--border-radius-2xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-inner { box-shadow: var(--shadow-inner) !important; }
.shadow-none { box-shadow: none !important; }

/* Transition Utilities */
.transition-fast { transition: all var(--transition-fast) !important; }
.transition { transition: all var(--transition-normal) !important; }
.transition-normal { transition: all var(--transition-normal) !important; }
.transition-slow { transition: all var(--transition-slow) !important; }

/* Bootstrap Customizations */

/* Button Overrides */
.btn {
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-normal);
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-sm);
  line-height: 1.25;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary-800) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
  color: var(--color-white);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--color-secondary-dark) 0%, var(--color-secondary-900) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-success {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-accent-dark) 100%);
  color: var(--color-white);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--color-accent-dark) 0%, #065f46 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-outline-light {
  border: 2px solid var(--color-white);
  color: var(--color-white);
  background: transparent;
}

.btn-outline-light:hover {
  background: var(--color-white);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Form Controls */
.form-control {
  border: 2px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  padding: 0.75rem 1rem;
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  background-color: var(--color-white);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  outline: none;
}

.form-control::placeholder {
  color: var(--color-gray-400);
}

.form-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-sm);
}

.form-select {
  border: 2px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  padding: 0.75rem 1rem;
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  background-color: var(--color-white);
}

.form-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  outline: none;
}

/* Card Overrides */
.card {
  border: none;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  background-color: var(--color-white);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0 !important;
  padding: 1.25rem 1.5rem;
  font-weight: var(--font-weight-semibold);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: var(--color-gray-50);
  border-top: 1px solid var(--color-gray-200);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl) !important;
  padding: 1rem 1.5rem;
}

/* Table Overrides */
.table {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background-color: var(--color-white);
}

.table thead th {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  font-weight: var(--font-weight-semibold);
  border: none;
  padding: 1rem;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tbody tr {
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--color-gray-50);
}

.table tbody td {
  padding: 1rem;
  border-color: var(--color-gray-200);
  vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--color-gray-50);
}

.table-striped tbody tr:nth-of-type(odd):hover {
  background-color: var(--color-gray-100);
}

/* Navigation Overrides */
.navbar {
  padding: 1rem 0;
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
}

.navbar-brand {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  color: var(--color-white) !important;
}

.nav-link {
  font-weight: var(--font-weight-medium);
  color: var(--color-white) !important;
  transition: var(--transition-normal);
  padding: 0.5rem 1rem !important;
  border-radius: var(--border-radius-md);
  margin: 0 0.25rem;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* Alert Overrides */
.alert {
  border: none;
  border-radius: var(--border-radius-lg);
  padding: 1rem 1.25rem;
  font-weight: var(--font-weight-medium);
}

.alert-success {
  background-color: var(--color-primary-50);
  color: var(--color-accent-dark);
  border-left: 4px solid var(--color-success);
}

.alert-danger {
  background-color: var(--color-primary-50);
  color: var(--color-primary-dark);
  border-left: 4px solid var(--color-error);
}

.alert-warning {
  background-color: #fef3c7;
  color: #92400e;
  border-left: 4px solid var(--color-warning);
}

.alert-info {
  background-color: var(--color-secondary-50);
  color: var(--color-secondary-dark);
  border-left: 4px solid var(--color-info);
}

/* Badge Overrides */
.badge {
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-xs);
}

.badge.bg-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-accent-dark) 100%) !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, var(--color-warning) 0%, #b45309 100%) !important;
}

.badge.bg-danger {
  background: linear-gradient(135deg, var(--color-error) 0%, var(--color-primary-800) 100%) !important;
}

/* Modal Overrides */
.modal-content {
  border: none;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
}

.modal-header {
  border-bottom: 1px solid var(--color-gray-200);
  padding: 1.5rem;
}

.modal-title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid var(--color-gray-200);
  padding: 1rem 1.5rem;
}

/* Dropdown Overrides */
.dropdown-menu {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 0.5rem 0;
}

.dropdown-item {
  padding: 0.75rem 1.25rem;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.dropdown-item:hover {
  background-color: var(--color-primary-50);
  color: var(--color-primary);
}

/* Custom Utility Classes */

/* Medical Theme Gradients */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 100%);
}

.gradient-medical {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

/* Text Gradients */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-medical {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hover Effects */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-approved {
  background-color: #d1fae5;
  color: #065f46;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-rejected {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

.status-cancelled {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

/* Blood Type Badges */
.blood-type-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: var(--border-radius-full);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  color: var(--color-white);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  box-shadow: var(--shadow-md);
}

.blood-type-badge.type-a { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
.blood-type-badge.type-b { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); }
.blood-type-badge.type-ab { background: linear-gradient(135deg, #059669 0%, #047857 100%); }
.blood-type-badge.type-o { background: linear-gradient(135deg, #d97706 0%, #b45309 100%); }

/* Urgency Indicators */
.urgency-low {
  background-color: #d1fae5;
  color: #065f46;
  border-left: 4px solid #10b981;
}

.urgency-medium {
  background-color: #fef3c7;
  color: #92400e;
  border-left: 4px solid #f59e0b;
}

.urgency-high {
  background-color: #fed7d7;
  color: #c53030;
  border-left: 4px solid #f56565;
}

.urgency-critical {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
  border-left: 4px solid var(--color-primary);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Utilities */
@media (max-width: 576px) {
  .text-responsive-sm { font-size: var(--font-size-sm) !important; }
  .text-responsive-base { font-size: var(--font-size-base) !important; }
  .text-responsive-lg { font-size: var(--font-size-lg) !important; }

  .card { margin-bottom: 1rem; }
  .btn { padding: 0.5rem 1rem; font-size: var(--font-size-sm); }
}

@media (max-width: 768px) {
  .navbar-brand { font-size: var(--font-size-lg) !important; }
  .card-body { padding: 1rem; }
  .modal-dialog { margin: 1rem; }
}

/* Micro-interactions and Enhanced Animations */

/* Bounce Animation */
.bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Shake Animation */
.shake {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}

/* Fade Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.fade-out {
  animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Slide Animations */
.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Zoom Animations */
.zoom-in {
  animation: zoomIn 0.5s ease-out;
}

.zoom-out {
  animation: zoomOut 0.5s ease-out;
}

@keyframes zoomIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes zoomOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0);
    opacity: 0;
  }
}

/* Rotate Animation */
.rotate {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Medical-themed Animations */
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

.blood-drop-animation {
  animation: bloodDrop 2s ease-in-out infinite;
}

@keyframes bloodDrop {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  25% {
    transform: translateY(-10px) scale(1.1);
  }
  50% {
    transform: translateY(5px) scale(0.9);
  }
  75% {
    transform: translateY(-5px) scale(1.05);
  }
}

/* Interactive States */
.interactive {
  cursor: pointer;
  transition: var(--transition-normal);
}

.interactive:hover {
  transform: translateY(-2px);
}

.interactive:active {
  transform: translateY(0);
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.focus-ring-inset:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading-skeleton 1.5s infinite;
}

@keyframes loading-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Notification Badges */
.notification-badge {
  position: relative;
}

.notification-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--color-primary);
  border-radius: 50%;
  border: 2px solid var(--color-white);
  animation: pulse 2s infinite;
}

/* Button Enhancements */
.btn-floating {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  transition: var(--transition-normal);
}

.btn-floating:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

/* Card Enhancements */
.card-hover-effect {
  transition: var(--transition-normal);
}

.card-hover-effect:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

/* Progress Indicators */
.progress-ring {
  position: relative;
  display: inline-block;
}

.progress-ring svg {
  transform: rotate(-90deg);
}

.progress-ring circle {
  fill: transparent;
  stroke: var(--color-gray-200);
  stroke-width: 4;
}

.progress-ring .progress-circle {
  stroke: var(--color-primary);
  stroke-dasharray: 0 100;
  transition: stroke-dasharray 0.5s ease;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .bounce,
  .shake,
  .fade-in,
  .fade-out,
  .slide-in-right,
  .slide-in-left,
  .slide-in-up,
  .slide-in-down,
  .zoom-in,
  .zoom-out,
  .rotate,
  .heartbeat,
  .blood-drop-animation,
  .loading-skeleton,
  .notification-badge::after {
    animation: none;
  }

  .interactive:hover,
  .btn-floating:hover,
  .card-hover-effect:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .focus-ring:focus,
  .focus-ring-inset:focus {
    outline-width: 3px;
  }

  .btn-floating {
    border: 2px solid var(--color-gray-800);
  }

  .notification-badge::after {
    border-width: 3px;
  }
}
