import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../core/services/auth.service';
import { LoginResponse } from '../../../models/auth.model';
import { Router, RouterLink } from '@angular/router';
import { APP_CONSTANTS, DESIGN_TOKENS, CSS_CLASSES } from '../../../constants';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, RouterLink, CommonModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading = false;
  showPassword = false;

  // Design System
  readonly DESIGN = DESIGN_TOKENS;
  readonly CSS = CSS_CLASSES;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(5)]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Check if user is already logged in
    if (this.authService.isLoggedIn().value) {
      this.redirectToUserDashboard();
    }
  }

  onLogin(): void {
    if (this.loginForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    const { email, password, rememberMe } = this.loginForm.value;

    this.authService.login(email, password).subscribe({
      next: (response: LoginResponse) => {
        this.isLoading = false;
        console.log('Login successful', response);

        // Handle remember me functionality
        if (rememberMe) {
          localStorage.setItem('rememberMe', 'true');
        }

        // Show success message
        Swal.fire({
          title: 'Welcome Back!',
          text: 'Login successful. Redirecting to your dashboard...',
          icon: 'success',
          timer: 2000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end'
        });

        // Navigate based on user type
        this.redirectBasedOnUserType(response);
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Login error:', error);

        let errorMessage = 'Login failed. Please check your credentials.';

        // Handle specific error cases
        if (error.status === 401) {
          errorMessage = 'Invalid email or password. Please try again.';
        } else if (error.status === 403) {
          errorMessage = 'Your account has been suspended. Please contact support.';
        } else if (error.status === 0) {
          errorMessage = 'Unable to connect to server. Please check your internet connection.';
        }

        Swal.fire({
          title: 'Login Failed',
          text: errorMessage,
          icon: 'error',
          confirmButtonText: 'Try Again',
          confirmButtonColor: DESIGN_TOKENS.COLORS.PRIMARY[600]
        });
      }
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isFieldValid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.valid && (field.dirty || field.touched));
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  private redirectBasedOnUserType(response: LoginResponse): void {
    const routes = {
      [APP_CONSTANTS.USER_TYPES.ADMIN]: APP_CONSTANTS.ROUTES.ADMIN,
      [APP_CONSTANTS.USER_TYPES.DONOR]: APP_CONSTANTS.ROUTES.DONOR_HOME,
      [APP_CONSTANTS.USER_TYPES.HOSPITAL]: APP_CONSTANTS.ROUTES.HOSPITAL
    };

    const route = routes[response.type];
    if (route) {
      this.router.navigate([route], { queryParams: { id: response.id } });
    } else {
      console.error('Unknown user type:', response.type);
      this.router.navigate(['/']);
    }
  }

  private redirectToUserDashboard(): void {
    // Get user data from storage and redirect to appropriate dashboard
    const userData = this.authService.getCurrentUser();
    if (userData) {
      this.redirectBasedOnUserType(userData);
    }
  }

  onLogout(): void {
    this.authService.logout();
    this.router.navigate([APP_CONSTANTS.ROUTES.LOGIN]);
  }
}
}