import { NgFor, CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { AfterViewInit, Component, Input, OnInit, OnChanges } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import Aos from 'aos';
import Swal from 'sweetalert2';
import { DESIGN_TOKENS, CSS_CLASSES, getBloodTypeClass, getStatusClass, getUrgencyClass } from '../../../constants';

@Component({
  selector: 'app-pending-list',
  standalone: true,
  imports: [NgFor, CommonModule, FormsModule],
  templateUrl: './pending-list.component.html',
  styleUrl: './pending-list.component.css'
})
export class PendingListComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() public appointments: any = [];
  public appointmentsApproved: any = [];
  public appprovedAppointment: any;
  public canceledAppointmentList: any;
  public bloodRequestList: any = [];
  public completedBloodRequest: any;
  public feedBackList: any;

  // Search terms
  public searchTerm: string = '';
  public bloodRequestSearchTerm: string = '';
  public feedbackSearchTerm: string = '';

  // Filtered data
  public filteredAppointments: any[] = [];
  public filteredBloodRequests: any[] = [];
  public filteredFeedback: any[] = [];

  // Design System
  readonly DESIGN = DESIGN_TOKENS;
  readonly CSS = CSS_CLASSES;

  constructor(private route: ActivatedRoute, private http: HttpClient ,private router: Router) {}
  


  ngAfterViewInit(): void {
    
  }

  ngOnInit(): void {
    Aos.init();
    console.log(this.appointments);
    this.loadApprovedAppointments();
    this.loadRequests();
    this.loadCanceledAppointment();
    this.approvedBloodRequestList();
    this.loadFeedBackTable();
    this.initializeFilters();
  }

  ngOnChanges(): void {
    this.initializeFilters();
  }

  private initializeFilters(): void {
    this.filteredAppointments = this.appointments || [];
    this.filteredBloodRequests = this.bloodRequestList || [];
    this.filteredFeedback = this.feedBackList || [];
  }

  loadFeedBackTable(){
    this.http.get("http://localhost:8080/FeedBack/all").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.feedBackList = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }

  loadApprovedAppointments(){
    this.http.get("http://localhost:8080/Appointment/approved").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.appointmentsApproved = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }

  navigate(){
    this.router.navigate(['/add-Hospital']);
  }

  approve(Appointment:any){
    const status = "APPROVED";  

    this.http.patch(`http://localhost:8080/Appointment/update/${Appointment.appointmentID}/${status}`, {}, { responseType: 'text' })
      .subscribe(
        data => {

          console.log("Appointment updated successfully:", data);
          Appointment.status = status;
          this.loadApprovedAppointments()
        },
        error => {
          console.error("Error updating appointment status:", error);
        }
      );
  }


  cancel(Appointment:any){
    const status = "CANCELED";

    this.http.patch(`http://localhost:8080/Appointment/update/${Appointment.appointmentID}/${status}`, {}, { responseType: 'text' })
      .subscribe(
        data => {
          console.log("Appointment updated successfully:", data);
          Appointment.status = status;
        },
        error => {
          console.error("Error updating appointment status:", error);
        }
      );
  }

  loadCanceledAppointment(){
    this.http.get("http://localhost:8080/Appointment/canceled").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.canceledAppointmentList = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }





  approveBlood(requests:any){
    const status = "COMPLETED";

    this.http.patch(`http://localhost:8080/BloodRequest/update/${requests.requestID}/${status}`, {}, { responseType: 'text' })
      .subscribe(
        data => {
          console.log("Appointment updated successfully:", data);
          requests.status = status;
          this.approvedBloodRequestList();
          this.loadRequests();
        },
        error => {
          console.error("Error updating appointment status:", error);
        }
      );
  }

  cancelBloodRequest(requests:any){
    const status = "CANCELED";

    this.http.patch(`http://localhost:8080/BloodRequest/update/${requests.requestID}/${status}`, {}, { responseType: 'text' })
      .subscribe(
        data => {
          console.log("Appointment updated successfully:", data);
          requests.status = status;
          this.approvedBloodRequestList();
          this.loadRequests();
        },
        error => {
          console.error("Error updating appointment status:", error);
        }
      );
  }

  approvedBloodRequestList(){
    this.http.get("http://localhost:8080/BloodRequest/completed").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.completedBloodRequest = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }

  loadRequests(){
    this.http.get("http://localhost:8080/BloodRequest/pending").subscribe(
      data => {
        console.log("Data loaded:", data);
        this.bloodRequestList = data;
        this.filteredBloodRequests = data;
      },
      error => {
        console.error("Error loading donor details:", error);
      }
    );
  }

  // New utility methods for enhanced UI
  getTotalAppointments(): number {
    return (this.appointments?.length || 0) +
           (this.appointmentsApproved?.length || 0) +
           (this.canceledAppointmentList?.length || 0);
  }

  getTotalBloodRequests(): number {
    return (this.bloodRequestList?.length || 0) +
           (this.completedBloodRequest?.length || 0);
  }

  // Search and filter methods
  onSearch(): void {
    this.filteredAppointments = this.appointments?.filter((appointment: any) =>
      appointment.appointmentID?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      appointment.donarID?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      appointment.bloodType?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      appointment.address?.toLowerCase().includes(this.searchTerm.toLowerCase())
    ) || [];
  }

  onBloodRequestSearch(): void {
    this.filteredBloodRequests = this.bloodRequestList?.filter((request: any) =>
      request.hospitalID?.toLowerCase().includes(this.bloodRequestSearchTerm.toLowerCase()) ||
      request.name?.toLowerCase().includes(this.bloodRequestSearchTerm.toLowerCase()) ||
      request.bloodType?.toLowerCase().includes(this.bloodRequestSearchTerm.toLowerCase()) ||
      request.contactNumber?.toLowerCase().includes(this.bloodRequestSearchTerm.toLowerCase())
    ) || [];
  }

  onFeedbackSearch(): void {
    this.filteredFeedback = this.feedBackList?.filter((feedback: any) =>
      feedback.feedbackID?.toLowerCase().includes(this.feedbackSearchTerm.toLowerCase()) ||
      feedback.text?.toLowerCase().includes(this.feedbackSearchTerm.toLowerCase())
    ) || [];
  }

  getFilteredAppointments(): any[] {
    return this.filteredAppointments;
  }

  getFilteredBloodRequests(): any[] {
    return this.filteredBloodRequests;
  }

  getFilteredFeedback(): any[] {
    return this.filteredFeedback;
  }

  // Design system helper methods
  getBloodTypeClass(bloodType: string): string {
    return getBloodTypeClass(bloodType);
  }

  getStatusClass(status: string): string {
    return getStatusClass(status);
  }

  getStatusIcon(status: string): string {
    const iconMap: { [key: string]: string } = {
      'PENDING': 'bi-clock-history',
      'APPROVED': 'bi-check-circle',
      'COMPLETED': 'bi-check-circle-fill',
      'CANCELLED': 'bi-x-circle',
      'REJECTED': 'bi-x-circle-fill'
    };
    return iconMap[status?.toUpperCase()] || 'bi-info-circle';
  }

  getUrgencyClass(urgency: string): string {
    return getUrgencyClass(urgency);
  }

  getUrgencyIcon(urgency: string): string {
    const iconMap: { [key: string]: string } = {
      'LOW': 'bi-info-circle',
      'MEDIUM': 'bi-exclamation-circle',
      'HIGH': 'bi-exclamation-triangle',
      'CRITICAL': 'bi-exclamation-triangle-fill'
    };
    return iconMap[urgency?.toUpperCase()] || 'bi-info-circle';
  }

  // Utility methods
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }

  getTimeAgo(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  }

  truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  getStarArray(rating: number): number[] {
    return Array(Math.floor(rating)).fill(0);
  }

}
