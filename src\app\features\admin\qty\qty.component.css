/* Modern Blood Inventory Card */
.modern-blood-card {
  background: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  transition: var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  position: relative;
  overflow: hidden;
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.modern-blood-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.modern-blood-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Card Header */
.blood-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.blood-type-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-full);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  color: var(--color-white);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  box-shadow: var(--shadow-sm);
}

.blood-type-badge.type-a {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.blood-type-badge.type-b {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.blood-type-badge.type-ab {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.blood-type-badge.type-o {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.blood-type-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
}

.status-indicator {
  width: 2rem;
  height: 2rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.status-indicator.status-critical {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
  animation: pulse 2s infinite;
}

.status-indicator.status-low {
  background: #fed7d7;
  color: #c53030;
}

.status-indicator.status-medium {
  background: #fef3c7;
  color: #92400e;
}

.status-indicator.status-good {
  background: #d1fae5;
  color: #065f46;
}

/* Card Body */
.blood-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Circular Progress */
.progress-container {
  margin-bottom: 1.5rem;
}

.circular-progress {
  --progress: 0%;
  --size: 120px;
  --thickness: 8px;

  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  background: conic-gradient(
    var(--color-primary) var(--progress),
    var(--color-gray-200) var(--progress)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: progressAnimation 2s ease-out;
}

.circular-progress::before {
  content: '';
  position: absolute;
  inset: var(--thickness);
  border-radius: 50%;
  background: var(--color-white);
}

.progress-inner {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-value {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.amount {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
}

.unit {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

@keyframes progressAnimation {
  from {
    --progress: 0%;
  }
}

/* Blood Info */
.blood-info {
  width: 100%;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--color-gray-100);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
}

/* Card Footer */
.blood-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid var(--color-gray-200);
}

.urgency-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.action-btn {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--border-radius-md);
  transition: var(--transition-normal);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-blood-card {
    padding: 1rem;
    min-height: 280px;
  }

  .blood-card-header {
    margin-bottom: 1rem;
  }

  .blood-type-badge {
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-xs);
  }

  .blood-type-text {
    font-size: var(--font-size-sm);
  }

  .circular-progress {
    --size: 100px;
    --thickness: 6px;
  }

  .amount {
    font-size: var(--font-size-lg);
  }

  .unit {
    font-size: 0.625rem;
  }

  .blood-card-footer {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .modern-blood-card {
    padding: 0.75rem;
    min-height: 260px;
  }

  .circular-progress {
    --size: 80px;
    --thickness: 5px;
  }

  .amount {
    font-size: var(--font-size-base);
  }

  .info-item {
    padding: 0.375rem 0;
  }

  .info-label,
  .info-value {
    font-size: 0.75rem;
  }

  .urgency-indicator {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .modern-blood-card,
  .circular-progress,
  .action-btn {
    animation: none;
    transition: none;
  }

  .status-indicator.status-critical {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .modern-blood-card {
    border: 2px solid var(--color-gray-800);
  }

  .blood-type-badge,
  .status-indicator,
  .urgency-indicator {
    border: 1px solid var(--color-gray-800);
  }
}

/* Focus States */
.action-btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .modern-blood-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--color-gray-400);
  }

  .action-btn {
    display: none;
  }
}
