/* Modern Toast Notification Styles */

/* Container Positioning */
.toast-container {
  position: fixed;
  z-index: var(--z-tooltip);
  pointer-events: none;
}

.toast-container.toast-top-right {
  top: 1rem;
  right: 1rem;
}

.toast-container.toast-top-left {
  top: 1rem;
  left: 1rem;
}

.toast-container.toast-bottom-right {
  bottom: 1rem;
  right: 1rem;
}

.toast-container.toast-bottom-left {
  bottom: 1rem;
  left: 1rem;
}

.toast-container.toast-top-center {
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
}

.toast-container.toast-bottom-center {
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
}

/* Toast Base Styles */
.toast {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  min-width: 300px;
  max-width: 400px;
  padding: 1rem;
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--color-gray-200);
  pointer-events: auto;
  position: relative;
  overflow: hidden;
}

/* Toast Type Styles */
.toast.toast-success {
  border-left: 4px solid var(--color-success);
}

.toast.toast-success .toast-icon {
  color: var(--color-success);
}

.toast.toast-error {
  border-left: 4px solid var(--color-error);
}

.toast.toast-error .toast-icon {
  color: var(--color-error);
}

.toast.toast-warning {
  border-left: 4px solid var(--color-warning);
}

.toast.toast-warning .toast-icon {
  color: var(--color-warning);
}

.toast.toast-info {
  border-left: 4px solid var(--color-info);
}

.toast.toast-info .toast-icon {
  color: var(--color-info);
}

/* Toast Icon */
.toast-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
  margin-top: 0.125rem;
}

/* Toast Content */
.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
  margin-bottom: 0.25rem;
  line-height: var(--line-height-tight);
}

.toast-message {
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  line-height: var(--line-height-normal);
  word-wrap: break-word;
}

/* Dismiss Button */
.toast-dismiss {
  background: none;
  border: none;
  color: var(--color-gray-400);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
}

.toast-dismiss:hover {
  color: var(--color-gray-600);
  background: var(--color-gray-100);
}

.toast-dismiss:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Progress Bar */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--color-gray-200);
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  width: 100%;
  background: var(--color-primary);
  transform-origin: left;
  animation: toastProgress linear forwards;
}

.toast.toast-success .toast-progress-bar {
  background: var(--color-success);
}

.toast.toast-error .toast-progress-bar {
  background: var(--color-error);
}

.toast.toast-warning .toast-progress-bar {
  background: var(--color-warning);
}

.toast.toast-info .toast-progress-bar {
  background: var(--color-info);
}

@keyframes toastProgress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Hover Effects */
.toast:hover .toast-progress-bar {
  animation-play-state: paused;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast-container {
    left: 1rem !important;
    right: 1rem !important;
    transform: none !important;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .toast-container {
    left: 0.5rem !important;
    right: 0.5rem !important;
  }
  
  .toast {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .toast-title,
  .toast-message {
    font-size: var(--font-size-xs);
  }
  
  .toast-icon {
    font-size: var(--font-size-base);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .toast-progress-bar {
    animation: none;
    transform: scaleX(0);
  }
}

@media (prefers-contrast: high) {
  .toast {
    border: 2px solid var(--color-gray-800);
  }
  
  .toast.toast-success {
    border-left: 6px solid var(--color-success);
  }
  
  .toast.toast-error {
    border-left: 6px solid var(--color-error);
  }
  
  .toast.toast-warning {
    border-left: 6px solid var(--color-warning);
  }
  
  .toast.toast-info {
    border-left: 6px solid var(--color-info);
  }
}

/* Print Styles */
@media print {
  .toast-container {
    display: none;
  }
}
