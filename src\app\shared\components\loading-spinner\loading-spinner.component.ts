import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading-spinner',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="modern-spinner-container" [ngClass]="containerClasses">
      <!-- Blood Drop Spinner -->
      <div *ngIf="type === 'blood-drop'" class="spinner-blood-drop" [ngClass]="sizeClass">
        <div class="blood-drop">
          <i class="bi bi-droplet-fill"></i>
        </div>
      </div>

      <!-- Heart Beat Spinner -->
      <div *ngIf="type === 'heartbeat'" class="spinner-heartbeat" [ngClass]="sizeClass">
        <div class="heartbeat">
          <i class="bi bi-heart-pulse-fill"></i>
        </div>
      </div>

      <!-- Pulse Dots Spinner -->
      <div *ngIf="type === 'pulse'" class="spinner-pulse" [ngClass]="sizeClass">
        <div class="pulse-dot pulse-dot-1"></div>
        <div class="pulse-dot pulse-dot-2"></div>
        <div class="pulse-dot pulse-dot-3"></div>
      </div>

      <!-- Circle Spinner -->
      <div *ngIf="type === 'circle'" class="spinner-circle" [ngClass]="sizeClass">
        <div class="circle-loader"></div>
      </div>

      <!-- Medical Cross Spinner -->
      <div *ngIf="type === 'medical'" class="spinner-medical" [ngClass]="sizeClass">
        <div class="medical-cross">
          <i class="bi bi-plus-circle-fill"></i>
        </div>
      </div>

      <!-- Loading Text -->
      <p *ngIf="message" class="spinner-message" [ngClass]="textClass">{{ message }}</p>
    </div>
  `,
  styleUrls: ['./loading-spinner.component.css']
})
export class LoadingSpinnerComponent {
  @Input() type: 'blood-drop' | 'heartbeat' | 'pulse' | 'circle' | 'medical' = 'pulse';
  @Input() size: 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() color: 'primary' | 'secondary' | 'white' | 'gray' = 'primary';
  @Input() message: string = '';
  @Input() overlay: boolean = false;
  @Input() fullscreen: boolean = false;
  @Input() center: boolean = true;

  get containerClasses(): string {
    const classes = [];

    if (this.overlay) classes.push('spinner-overlay');
    if (this.fullscreen) classes.push('spinner-fullscreen');
    if (this.center) classes.push('spinner-center');

    return classes.join(' ');
  }

  get sizeClass(): string {
    return `spinner-${this.size}`;
  }

  get textClass(): string {
    return `text-${this.color}`;
  }
}
