/* Modern Header Styles */
.modern-header {
  position: relative;
  z-index: var(--z-sticky);
}

.navbar {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 0;
  transition: var(--transition-normal);
}

.navbar.scrolled {
  padding: 0.5rem 0;
  box-shadow: var(--shadow-xl);
}

/* Brand Section */
.brand-logo-container {
  position: relative;
  width: 60px;
  height: 60px;
  background: var(--color-white);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.brand-logo-container:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.brand-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.brand-text {
  color: var(--color-white);
}

.brand-title {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-white) 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.9);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Mobile Toggle */
.navbar-toggler {
  border: none !important;
  padding: 0.5rem;
  border-radius: var(--border-radius-md);
  background: rgba(255, 255, 255, 0.1);
  transition: var(--transition-normal);
}

.navbar-toggler:hover {
  background: rgba(255, 255, 255, 0.2);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.navbar-toggler-icon-custom {
  color: var(--color-white);
}

/* Navigation Links */
.nav-link-modern {
  color: var(--color-white) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-sm) !important;
  padding: 0.75rem 1rem !important;
  border-radius: var(--border-radius-md) !important;
  transition: var(--transition-normal) !important;
  position: relative;
  display: flex;
  align-items: center;
  text-decoration: none;
}

.nav-link-modern:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: var(--color-white) !important;
  transform: translateY(-1px);
}

.nav-link-modern.active {
  background: rgba(255, 255, 255, 0.15) !important;
  color: var(--color-white) !important;
  font-weight: var(--font-weight-semibold) !important;
}

.nav-link-modern.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: var(--color-white);
  border-radius: var(--border-radius-full);
}

/* Action Buttons */
.navbar-actions {
  margin-left: auto;
}

.btn-action {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-lg);
  border: 2px solid transparent;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  text-decoration: none;
  white-space: nowrap;
}

.btn-outline-light.btn-action {
  border-color: rgba(255, 255, 255, 0.8);
  color: var(--color-white);
  background: transparent;
}

.btn-outline-light.btn-action:hover {
  background: var(--color-white);
  color: var(--color-primary);
  border-color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-light.btn-action {
  background: var(--color-white);
  color: var(--color-primary);
  border-color: var(--color-white);
}

.btn-light.btn-action:hover {
  background: var(--color-gray-100);
  color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* User Dropdown */
.dropdown-menu {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  min-width: 200px;
}

.dropdown-item {
  padding: 0.75rem 1.25rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
}

.dropdown-item:hover {
  background: var(--color-primary-50);
  color: var(--color-primary);
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: var(--color-gray-200);
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
  border-bottom: 1px solid var(--color-gray-200);
}

.breadcrumb {
  background: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-item {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.breadcrumb-item a {
  color: var(--color-primary);
  transition: var(--transition-fast);
}

.breadcrumb-item a:hover {
  color: var(--color-primary-dark);
}

.breadcrumb-item.active {
  color: var(--color-gray-600);
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: var(--color-gray-400);
  font-weight: var(--font-weight-bold);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .navbar {
    padding: 0.75rem 0;
  }

  .brand-text {
    display: none;
  }

  .brand-logo-container {
    width: 50px;
    height: 50px;
  }

  .brand-logo {
    width: 30px;
    height: 30px;
  }

  .navbar-collapse {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .navbar-nav {
    margin-bottom: 1rem;
  }

  .nav-link-modern {
    padding: 1rem !important;
    margin: 0.25rem 0;
    border-radius: var(--border-radius-lg) !important;
  }

  .navbar-actions {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }

  .btn-action {
    width: 100%;
    justify-content: center;
    padding: 1rem;
  }
}

@media (max-width: 575.98px) {
  .navbar {
    padding: 0.5rem 0;
  }

  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .brand-logo-container {
    width: 45px;
    height: 45px;
  }

  .brand-logo {
    width: 25px;
    height: 25px;
  }

  .btn-action {
    font-size: var(--font-size-xs);
    padding: 0.75rem 1rem;
  }

  .breadcrumb-nav {
    padding: 0.5rem 0;
  }

  .breadcrumb-item {
    font-size: var(--font-size-xs);
  }
}

/* Animation for mobile menu */
.navbar-collapse {
  transition: var(--transition-normal);
}

.navbar-collapse.collapsing {
  transition: height var(--transition-normal) ease;
}

/* Scroll behavior */
@media (prefers-reduced-motion: no-preference) {
  .navbar {
    transition: all var(--transition-normal) ease;
  }

  .navbar.navbar-scrolled {
    padding: 0.5rem 0;
    backdrop-filter: blur(15px);
  }
}

/* Focus states for accessibility */
.nav-link-modern:focus,
.btn-action:focus,
.navbar-toggler:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navbar {
    border-bottom: 2px solid var(--color-white);
  }

  .nav-link-modern,
  .btn-action {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}




/* sign button animation */
.hovering {
    background-color: #fdfdfd;  /* Initial color */
    color: rgb(0, 0, 0);       /* Optional padding */
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease; /* Smooth transition */
  }
  
  .hovering:hover {
    background: #ED213A;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to right, #93291E, #ED213A);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to right, #93291E, #ED213A); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
    color: #ffffff;
  }
  

  .nav-link {
    position: relative;
    color: black; /* Original text color */
    text-decoration: none; /* Remove default underline */
    padding-bottom: 4px; /* Space for underline effect */
    transition: color 0.3s ease; /* Smooth transition for color change */
  }
  
  .nav-link:hover {
    color: red; /* Optional: change color on hover */
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px; /* Thickness of the underline */
    bottom: 0;
    left: 0;
    background-color: rgb(255, 255, 255); /* Color of the underline */
    transition: width 0.3s ease; /* Smooth transition for the underline */
  }
  
  .nav-link:hover::after {
    width: 100%; /* Full width on hover */
  }
  