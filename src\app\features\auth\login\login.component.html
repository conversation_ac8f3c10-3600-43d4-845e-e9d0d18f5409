<section class="modern-login-section">
  <div class="container-fluid h-100">
    <div class="row h-100 g-0">
      <!-- Left Side - Hero Image -->
      <div class="col-lg-6 d-none d-lg-flex">
        <div class="login-hero">
          <div class="hero-overlay">
            <div class="hero-content">
              <div class="hero-logo mb-4">
                <i class="bi bi-heart-pulse text-white" style="font-size: 4rem;"></i>
              </div>
              <h1 class="hero-title text-white mb-3">Save Lives Together</h1>
              <p class="hero-subtitle text-white-50 mb-4">
                Join our community of life-savers. Every donation counts, every donor matters.
              </p>
              <div class="hero-stats">
                <div class="stat-item">
                  <div class="stat-number">10,000+</div>
                  <div class="stat-label">Lives Saved</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">5,000+</div>
                  <div class="stat-label">Active Donors</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">50+</div>
                  <div class="stat-label">Partner Hospitals</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="col-lg-6 d-flex align-items-center justify-content-center">
        <div class="login-form-container">
          <div class="login-header text-center mb-5">
            <div class="brand-logo mb-3">
              <i class="bi bi-droplet-fill text-primary" style="font-size: 3rem;"></i>
            </div>
            <h1 class="login-title text-gradient-primary mb-2">Welcome Back</h1>
            <p class="login-subtitle text-gray-600">Sign in to your LifeFlow account</p>
          </div>

          <!-- Login Form -->
          <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="modern-form" novalidate>
            <!-- Email Field -->
            <div class="form-group mb-4">
              <label for="email" class="form-label">
                <i class="bi bi-envelope me-2"></i>
                Email Address
              </label>
              <div class="input-wrapper">
                <input
                  type="email"
                  id="email"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid('email')"
                  [class.is-valid]="isFieldValid('email')"
                  formControlName="email"
                  placeholder="Enter your email address"
                  autocomplete="email"
                  aria-describedby="email-error" />
                <div class="input-icon">
                  <i class="bi bi-envelope text-gray-400"></i>
                </div>
              </div>
              <div *ngIf="isFieldInvalid('email')" class="invalid-feedback" id="email-error">
                <div *ngIf="loginForm.get('email')?.errors?.['required']">
                  <i class="bi bi-exclamation-circle me-1"></i>
                  Email is required
                </div>
                <div *ngIf="loginForm.get('email')?.errors?.['email']">
                  <i class="bi bi-exclamation-circle me-1"></i>
                  Please enter a valid email address
                </div>
              </div>
            </div>

            <!-- Password Field -->
            <div class="form-group mb-4">
              <label for="password" class="form-label">
                <i class="bi bi-lock me-2"></i>
                Password
              </label>
              <div class="input-wrapper">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  id="password"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid('password')"
                  [class.is-valid]="isFieldValid('password')"
                  formControlName="password"
                  placeholder="Enter your password"
                  autocomplete="current-password"
                  aria-describedby="password-error" />
                <button
                  type="button"
                  class="password-toggle"
                  (click)="togglePasswordVisibility()"
                  [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'">
                  <i class="bi" [ngClass]="showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
                </button>
              </div>
              <div *ngIf="isFieldInvalid('password')" class="invalid-feedback" id="password-error">
                <div *ngIf="loginForm.get('password')?.errors?.['required']">
                  <i class="bi bi-exclamation-circle me-1"></i>
                  Password is required
                </div>
                <div *ngIf="loginForm.get('password')?.errors?.['minlength']">
                  <i class="bi bi-exclamation-circle me-1"></i>
                  Password must be at least 5 characters long
                </div>
              </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="form-options d-flex justify-content-between align-items-center mb-4">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="rememberMe" formControlName="rememberMe">
                <label class="form-check-label" for="rememberMe">
                  Remember me
                </label>
              </div>
              <a href="#" class="forgot-password-link">
                Forgot password?
              </a>
            </div>

            <!-- Submit Button -->
            <button
              type="submit"
              class="btn btn-primary btn-lg w-100 mb-4 hover-lift"
              [disabled]="loginForm.invalid || isLoading"
              [attr.aria-label]="isLoading ? 'Signing in...' : 'Sign in to account'">
              <span *ngIf="!isLoading" class="d-flex align-items-center justify-content-center">
                <i class="bi bi-box-arrow-in-right me-2"></i>
                Sign In
              </span>
              <span *ngIf="isLoading" class="d-flex align-items-center justify-content-center">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Signing in...
              </span>
            </button>

            <!-- Divider -->
            <div class="divider mb-4">
              <span>or</span>
            </div>

            <!-- Social Login Options -->
            <div class="social-login mb-4">
              <button type="button" class="btn btn-outline-secondary w-100 mb-2 hover-lift">
                <i class="bi bi-google me-2"></i>
                Continue with Google
              </button>
            </div>

            <!-- Sign Up Link -->
            <div class="signup-link text-center">
              <p class="mb-0 text-gray-600">
                Don't have an account?
                <a routerLink="/DonarReg" class="text-primary fw-semibold text-decoration-none hover-underline">
                  Register here
                </a>
              </p>
            </div>
          </form>

          <!-- Footer Links -->
          <div class="login-footer text-center mt-4">
            <div class="footer-links">
              <a href="#" class="text-gray-500 text-decoration-none me-3">Terms of Service</a>
              <a href="#" class="text-gray-500 text-decoration-none">Privacy Policy</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
